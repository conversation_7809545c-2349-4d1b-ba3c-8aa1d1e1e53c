import tiktoken
from .embedding_manager import Embedding<PERSON>anager
from .chromadb_store import ChromaDBStore # Assuming this is your VectorStoreInterface implementation
from .summarizer import Summarizer # Import Summarizer for type hinting
from typing import Optional # Ensure Optional is imported

DEFAULT_MAX_CONTEXT_TOKENS = 3800 # Max tokens for the context window (leaving some for response)
DEFAULT_TOP_K_SIMILAR_HISTORY = 3 
DEFAULT_TOP_K_EXAMPLES = 2 # How many curated examples to retrieve
DEFAULT_RECENT_HISTORY_COUNT = 10 # How many recent messages to always include (if they fit)

class ContextRetriever:
    def __init__(self,
                 embedding_manager: EmbeddingManager,
                 chat_history_vector_store: ChromaDBStore,
                 examples_vector_store: ChromaDBStore = None,
                 summarizer: Optional[Summarizer] = None, # Added summarizer parameter
                 model_name_for_tokenizer: str = "gpt-3.5-turbo", # For tiktoken
                 max_context_tokens: int = DEFAULT_MAX_CONTEXT_TOKENS,
                 top_k_similar_history: int = DEFAULT_TOP_K_SIMILAR_HISTORY,
                 top_k_examples: int = DEFAULT_TOP_K_EXAMPLES,
                 recent_history_count: int = DEFAULT_RECENT_HISTORY_COUNT):
        self.embedding_manager = embedding_manager
        self.summarizer = summarizer # Store the summarizer instance
        self.chat_history_vector_store = chat_history_vector_store
        self.examples_vector_store = examples_vector_store
        try:
            self.tokenizer = tiktoken.encoding_for_model(model_name_for_tokenizer)
        except KeyError:
            print(f"Warning: Model {model_name_for_tokenizer} not found for tiktoken. Using cl100k_base.")
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        self.max_context_tokens = max_context_tokens
        self.top_k_similar_history = top_k_similar_history
        self.top_k_examples = top_k_examples
        self.recent_history_count = recent_history_count

    def _count_tokens(self, text: str) -> int:
        """Counts tokens in a string."""
        return len(self.tokenizer.encode(text))

    def _format_retrieved_doc(self, doc_text: str, source: str) -> dict:
        """Formats a retrieved document as a system-like message for context."""
        # This is a simple way; you might want user/assistant roles if docs are conversational
        return {"role": "system", "content": f"Context from {source}:\n{doc_text}"}

    def retrieve_and_build_context(self,
                                   current_query: str,
                                   full_message_history: list,
                                   recognized_intent: str = None) -> list:
        """
        Retrieves relevant context from chat history and curated examples,
        combines it with recent messages, and prepares it for the LLM.
        """
        if not full_message_history:
            return []

        # 1. System Prompt (first message in history, potentially modified by intent) and User Query
        system_prompt_message = dict(full_message_history[0]) # Make a copy
        if recognized_intent:
            system_prompt_message["content"] += f"\nUser's recognized intent: {recognized_intent}. Please consider this."
        
        # The current_query string is passed separately, full_message_history already contains it as the last item.
        # user_query_message will represent the actual last message(s) to be appended after context.
        user_query_message = dict(full_message_history[-1])

        # 3. Intermediate history (between system prompt and current user query)
        # This will be adjusted if the last messages form a tool call sequence.
        intermediate_history_proper = []
        if len(full_message_history) > 1: # Requires at least a system prompt and one other message
            intermediate_history_proper = [dict(m) for m in full_message_history[1:-1]]
        
        # 4. Retrieve Curated Examples
        retrieved_examples_formatted = []
        if self.examples_vector_store and self.top_k_examples > 0:
            query_embedding = self.embedding_manager.generate_embedding(current_query)
            if query_embedding is not None:
                try:
                    example_results_tuples = self.examples_vector_store.search_similar(
                        query_embedding=query_embedding, # Corrected keyword argument
                        top_k=self.top_k_examples,
                        # include=["documents", "metadatas"] # Assuming query returns this
                    )
                        # ChromaDB query returns a dict with 'ids', 'embeddings', 'metadatas', 'documents'
                        # We are interested in 'documents' (the text) and potentially 'metadatas'
                        # If search_similar returns List[Tuple[Dict[str, Any], float]]
                        # and the Dict has 'text' and 'metadata'
                    if example_results_tuples:
                        for doc_dict, _score in example_results_tuples:
                            text_content = doc_dict.get("text")
                            if text_content:
                                # You might want to use metadata (doc_dict.get("metadata")) to format it better
                                # For now, just use the text_content which was 'text_to_embed'
                                retrieved_examples_formatted.append(self._format_retrieved_doc(text_content, "Curated Example"))
                    print(f"Retrieved {len(retrieved_examples_formatted)} examples.") # This line was also part of the try
                except Exception as e:
                    print(f"Error querying examples vector store: {e}")

        # 5. Retrieve Similar Chat History
        retrieved_history_formatted = []
        if self.chat_history_vector_store and self.top_k_similar_history > 0 and intermediate_history_proper:
            query_embedding = self.embedding_manager.generate_embedding(current_query) # Could reuse if not modified
            if query_embedding is not None:
                try:
                    # We query against the text content of past user/assistant messages
                    # For simplicity, let's assume the vector store contains these directly.
                    # A more robust way would be to ensure only relevant parts of history are in the store.
                    history_results_tuples = self.chat_history_vector_store.search_similar( # Corrected call
                        query_embedding=query_embedding, # Corrected keyword argument
                        top_k=self.top_k_similar_history,
                        # where={"role": {"$in": ["user", "assistant"]}} # If metadata allows filtering
                    )
                    if history_results_tuples:
                        for doc_dict, _score in history_results_tuples:
                            original_text_content = doc_dict.get("text")
                            if original_text_content:
                                # Here, we might want to fetch the original message dict if we stored IDs
                                # For now, format as system context.
                                processed_text_content = original_text_content
                                # Summarize if too long and summarizer is available
                                MIN_RETRIEVED_HISTORY_LENGTH_FOR_SUMMARY_CHARS = 750 # Example threshold
                                if self.summarizer and len(original_text_content) > MIN_RETRIEVED_HISTORY_LENGTH_FOR_SUMMARY_CHARS:
                                    print(f"ContextRetriever: Retrieved history snippet is long ({len(original_text_content)} chars). Attempting to summarize...")
                                    summarized_content = self.summarizer.summarize_text(
                                        original_text_content,
                                        context_hint="past chat message",
                                        min_length_to_summarize_chars=MIN_RETRIEVED_HISTORY_LENGTH_FOR_SUMMARY_CHARS
                                    )
                                    processed_text_content = summarized_content or original_text_content # Use summary or fallback
                                retrieved_history_formatted.append(self._format_retrieved_doc(processed_text_content, "Similar Past Chat"))
                    print(f"Retrieved {len(retrieved_history_formatted)} similar history messages.")
                except Exception as e:
                    print(f"Error querying chat history vector store: {e}")

        # 6. Define fixed suffix and adjust intermediate history for tool call sequences
        final_fixed_suffix = [user_query_message] # Default: the very last message from input history

        # Check if the last message is a tool response preceded by an assistant tool call
        if len(full_message_history) >= 2:
            last_msg_obj = full_message_history[-1] # This is user_query_message
            second_last_msg_obj = full_message_history[-2]

            if last_msg_obj.get("role") == "tool" and \
               second_last_msg_obj.get("role") == "assistant" and \
               second_last_msg_obj.get("tool_calls"):
                # This is a tool sequence. Preserve assistant_tool_call and tool_response together.
                final_fixed_suffix = [dict(second_last_msg_obj), dict(last_msg_obj)]
                # Adjust intermediate_history_proper to exclude the second_last_msg_obj
                if intermediate_history_proper and intermediate_history_proper[-1] == second_last_msg_obj:
                    intermediate_history_proper = intermediate_history_proper[:-1]

        # Assemble the block of messages that can be truncated.
        # Order: Examples, Similar History, then recent actual turns from (adjusted) intermediate_history_proper.
        messages_for_middle_block = []
        messages_for_middle_block.extend(retrieved_examples_formatted)
        messages_for_middle_block.extend(retrieved_history_formatted)

        # Prepare to add recent messages, attempting to avoid duplicates from retrieved_history_formatted.
        # This is a simplified content-based deduplication. A more robust method would use unique message IDs.
        retrieved_history_contents = set()
        for r_msg in retrieved_history_formatted:
            # Extract the core content from the formatted system message.
            # Assumes _format_retrieved_doc creates "Context from {source}:\n{actual_content}"
            content_parts = r_msg.get("content", "").split(":\n", 1)
            if len(content_parts) > 1:
                retrieved_history_contents.add(content_parts[1])

        # Add relevant parts of (adjusted) intermediate_history_proper to messages_for_middle_block
        recent_intermediate_to_add = intermediate_history_proper[-self.recent_history_count:]
        added_recent_count = 0
        for recent_msg in recent_intermediate_to_add:
            if recent_msg.get("content") not in retrieved_history_contents:
                messages_for_middle_block.append(recent_msg) # Add to the end of middle block candidates
                added_recent_count += 1
        print(f"Added {added_recent_count} recent history messages (after deduplication attempt).")
        
        # 7. Truncate to fit max_context_tokens
        # Fixed parts are: system_prompt_message and final_fixed_suffix.
        # The messages_for_middle_block will be truncated to fit the remaining space.
        
        fixed_tokens = self._count_tokens(system_prompt_message["content"])
        for msg in final_fixed_suffix:
            fixed_tokens += self._count_tokens(msg.get("content", ""))
        
        available_tokens_for_context = self.max_context_tokens - fixed_tokens
        
        truncated_middle_block = []
        current_context_block_tokens = 0

        # Add context from the end (more recent / more relevant due to retrieval order)
        # reversed() iterates from the last item (most recent from intermediate history or last retrieved in messages_for_middle_block)
        for msg in reversed(messages_for_middle_block):
            msg_tokens = self._count_tokens(msg.get("content", ""))
            if current_context_block_tokens + msg_tokens <= available_tokens_for_context:
                truncated_middle_block.append(msg)
                current_context_block_tokens += msg_tokens
            else:
                break # Stop if adding next message exceeds limit
        
        truncated_middle_block.reverse() # Put back in original chronological/preference order

        final_assembled_messages = [system_prompt_message] + truncated_middle_block + final_fixed_suffix
        
        final_tokens = sum(self._count_tokens(m.get("content", "")) for m in final_assembled_messages)
        print(f"Context assembly: Original msgs {len(full_message_history)}, Final msgs for LLM {len(final_assembled_messages)}, Tokens: {final_tokens}/{self.max_context_tokens}")
        return final_assembled_messages