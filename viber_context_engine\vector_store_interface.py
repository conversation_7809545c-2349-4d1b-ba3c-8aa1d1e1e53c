from abc import ABC, abstractmethod
from typing import List, Tu<PERSON>, Dict, Any
import numpy as np

class VectorStoreInterface(ABC):
    """
    Abstract base class defining the interface for a vector store.
    Any vector store implementation (e.g., ChromaDB, FAISS) should inherit
    from this class and implement its methods.
    """

    @abstractmethod
    def add_documents(self, documents: List[Dict[str, Any]], embeddings: List[np.ndarray]) -> None:
        """
        Adds documents and their corresponding embeddings to the vector store.

        Args:
            documents (List[Dict[str, Any]]): A list of documents. Each document is a dictionary
                                              that should at least contain an 'id' and 'text' field,
                                              plus any other metadata.
            embeddings (List[np.ndarray]): A list of embeddings, where each embedding
                                           corresponds to a document in the `documents` list.
        """
        pass

    @abstractmethod
    def search_similar(self, query_embedding: np.ndarray, top_k: int = 5) -> List[Tuple[Dict[str, Any], float]]:
        """
        Searches for the top_k most similar documents to a given query embedding.

        Args:
            query_embedding (np.ndarray): The embedding of the query text.
            top_k (int): The number of most similar documents to retrieve.

        Returns:
            List[Tuple[Dict[str, Any], float]]: A list of tuples, where each tuple contains
                                                a document (dictionary) and its similarity score.
                                                The list is typically sorted by similarity.
        """
        pass