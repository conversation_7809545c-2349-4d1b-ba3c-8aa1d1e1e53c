[{"description": "Python function to read a CSV file into a list of dictionaries using csv.DictReader.", "example_id": "csv_dictreader_001", "text_to_embed": "Python function to read a CSV file into a list of dictionaries using csv.DictReader. Python read CSV to list of dictionaries, parse CSV file with headers, csv.DictReader example. How do I read a CSV file into a dictionary in Python? Python code to parse CSV into dict. How to convert CSV to dictionary in Python", "ideal_code": "import csv\n\ndef read_csv_to_dict(file_path):\n    \"\"\"Reads a CSV file into a list of dictionaries.\"\"\"\n    data = []\n    with open(file_path, mode='r', encoding='utf-8') as file:\n        csv_reader = csv.DictReader(file)\n        for row in csv_reader: # Appending the row directly as <PERSON><PERSON><PERSON><PERSON><PERSON> already makes it a dict\n            data.append(row)\n    return data\n\n# Example usage:\n# Assuming 'data.csv' exists with headers 'name,age'\n# and rows 'Alice,30', 'Bob,24'\n# try:\n#     people_data = read_csv_to_dict('data.csv')\n#     if people_data:\n#         print(people_data)\n#     # Output: [{'name': 'Alice', 'age': '30'}, {'name': '<PERSON>', 'age': '24'}]\n# except FileNotFoundError:\n#     print(f\"Error: The file data.csv was not found.\")\n# except Exception as e:\n#     print(f\"An error occurred: {e}\")", "explanation": "This function uses csv.DictReader to read CSV rows as dictionaries, making data access by column header easy and handles file operations safely.", "tags": ["python", "csv", "read file", "dictionary", "DictReader", "file system"]}, {"description": "Python function to calculate factorial recursively with input validation for non-negative integers.", "example_id": "factorial_recursive_validation_001", "text_to_embed": "Python function to calculate factorial recursively with input validation for non-negative integers. Calculate factorial in Python recursively, compute factorial, recursive factorial function. Write a Python function to calculate factorial recursively. How to compute factorial in Python using recursion. Python recursive factorial implementation", "ideal_code": "def factorial_recursive(n):\n    \"\"\"Calculates factorial of n using recursion.\"\"\"\n    if not isinstance(n, int):\n        raise TypeError('Input must be an integer.')\n    if n < 0:\n        raise ValueError('Factorial is not defined for negative numbers.')\n    elif n == 0 or n == 1:\n        return 1\n    else:\n        return n * factorial_recursive(n - 1)\n\n# Example usage:\n# try:\n#     print(factorial_recursive(5))  # Output: 120\n#     print(factorial_recursive(0))  # Output: 1\n#     # print(factorial_recursive(-1)) # Raises ValueError\n#     # print(factorial_recursive(5.5)) # Raises TypeError\n# except (ValueError, TypeError) as e:\n#     print(e)", "explanation": "Calculates factorial using recursion, including input validation for non-negative integers and defining base cases for 0 and 1.", "tags": ["python", "factorial", "recursion", "math", "function", "recursive", "validation"]}, {"description": "Python function to calculate factorial iteratively with input validation.", "example_id": "factorial_iterative_validation_A01", "text_to_embed": "Python function to calculate factorial iteratively with input validation. Calculate factorial in Python iteratively, compute factorial, iterative factorial function. Write a Python function to calculate factorial iteratively. How to compute factorial in Python using a loop. Python iterative factorial implementation", "ideal_code": "def factorial_iterative(n):\n    if not isinstance(n, int):\n        raise TypeError('Input must be an integer.')\n    if n < 0:\n        raise ValueError('Factorial is not defined for negative numbers.')\n    if n == 0 or n == 1:\n        return 1\n    result = 1\n    for i in range(2, n + 1):\n        result *= i\n    return result\n\n# Example usage:\n# try:\n#     print(factorial_iterative(5))  # Output: 120\n#     print(factorial_iterative(0))  # Output: 1\n#     # print(factorial_iterative(-1)) # Raises ValueError\n#     # print(factorial_iterative(5.5)) # Raises TypeError\n# except (ValueError, TypeError) as e:\n#     print(e)", "explanation": "Calculates factorial iteratively using a loop. Includes validation for non-negative integer inputs and handles base cases.", "tags": ["python", "factorial", "math", "iteration", "loop", "function", "iterative", "validation"]}, {"description": "Python class `Person` with `__init__` constructor, `greet` and `is_adult` methods.", "example_id": "person_class_methods_003", "text_to_embed": "Python class `Person` with `__init__` constructor, `greet` and `is_adult` methods. Python simple class definition, OOP Person example, __init__ constructor and methods. How to create a simple class in Python for a person. Python class example with constructor and method. Basic OOP: Python Person class", "ideal_code": "class Person:\n    \"\"\"A simple class representing a person.\"\"\"\n    def __init__(self, name, age):\n        if not isinstance(name, str) or not name.strip():\n            raise ValueError(\"Name must be a non-empty string.\")\n        if not isinstance(age, int) or age < 0:\n            raise ValueError(\"Age must be a non-negative integer.\")\n        self.name = name\n        self.age = age\n\n    def greet(self):\n        return f'Hello, my name is {self.name} and I am {self.age} years old.'\n\n    def is_adult(self):\n        return self.age >= 18\n\n# Example usage:\n# try:\n#     person1 = Person('Alice', 30)\n#     print(person1.greet())\n#     print(f'{person1.name} is an adult: {person1.is_adult()}')\n#     # person2 = Person('', 25) # Raises ValueError\n#     # person3 = Person('Bob', -5) # Raises ValueError\n# except ValueError as e:\n#     print(f\"Error: {e}\")\n# Output:\n# Hello, my name is <PERSON> and I am 30 years old.\n# Alice is an adult: True", "explanation": "Defines a `Person` class with an `__init__` constructor for name and age (with validation), a `greet` method, and an `is_adult` method.", "tags": ["python", "class", "oop", "object-oriented", "constructor", "__init__", "methods", "validation"]}, {"description": "Python function for binary search on a sorted list, returning index or -1.", "example_id": "binary_search_sortedlist_004", "text_to_embed": "Python function for binary search on a sorted list, returning index or -1. Python binary search algorithm, find item in sorted list, efficient search O(log n). Implement binary search in Python for a sorted list. How to write binary search algorithm in Python. Python efficient search in sorted array", "ideal_code": "def binary_search(sorted_list, item):\n    \"\"\"Performs binary search on a sorted list.\n    Returns the index of the item if found, otherwise -1.\n    \"\"\"\n    low = 0\n    high = len(sorted_list) - 1\n\n    while low <= high:\n        mid = (low + high) // 2 # Integer division\n        guess = sorted_list[mid]\n        if guess == item:\n            return mid  # Item found\n        if guess > item:\n            high = mid - 1 # Search in the left half\n        else:\n            low = mid + 1   # Search in the right half\n    return -1  # Item not found\n\n# Example usage:\n# my_list = [1, 3, 5, 7, 9, 11, 13]\n# print(f\"Index of 7: {binary_search(my_list, 7)}\")   # Output: Index of 7: 3\n# print(f\"Index of -1: {binary_search(my_list, -1)}\")  # Output: Index of -1: -1\n# print(f\"Index of 13: {binary_search(my_list, 13)}\") # Output: Index of 13: 6\n# print(f\"Index of 1: {binary_search(my_list, 1)}\")   # Output: Index of 1: 0\n# print(f\"Search in empty list: {binary_search([], 5)}\") # Output: Search in empty list: -1", "explanation": "Implements binary search on a sorted list by repeatedly dividing the search interval. Returns item's index or -1 if not found. O(log n) complexity.", "tags": ["python", "algorithm", "search", "binary search", "sorted list", "O(log n)"]}, {"description": "Python function to reverse a string using concise slice notation.", "example_id": "string_reverse_slice_005", "text_to_embed": "Python function to reverse a string using concise slice notation. Python reverse string, string slicing reverse, function to flip string characters. How to reverse a string in Python? Python function to reverse string. Easiest way to reverse string Python", "ideal_code": "def reverse_string(s):\n    \"\"\"Reverses a string using slicing.\"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string.\")\n    return s[::-1]\n\n# Example usage:\n# original_string = \"hello world\"\n# try:\n#     reversed_str = reverse_string(original_string)\n#     print(f'Original: \"{original_string}\", Reversed: \"{reversed_str}\"')\n#     # print(reverse_string(123)) # Raises TypeError\n# except TypeError as e:\n#     print(f\"Error: {e}\")\n# Output: Original: \"hello world\", Reversed: \"dlrow olleh\"", "explanation": "Reverses a string using Python's string slicing `[::-1]`. Includes type checking for string input.", "tags": ["python", "string", "reverse", "slicing", "text manipulation"]}, {"description": "Python function to fetch data from a URL using requests.get, handling JSON response and errors.", "example_id": "requests_get_json_006", "text_to_embed": "Python function to fetch data from a URL using requests.get, handling JSON response and errors. Python GET request, requests library example, fetch API data, handle HTTP errors requests. How to make a GET request in Python using requests library? Python fetch data from API URL. Example of requests.get in Python", "ideal_code": "import requests\n\ndef fetch_url_data(url):\n    \"\"\"Fetches data from a URL using a GET request and returns JSON if possible.\"\"\"\n    try:\n        response = requests.get(url, timeout=10) # Added timeout for robustness\n        response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)\n        try:\n            return response.json() # Attempt to parse JSON\n        except requests.exceptions.JSONDecodeError:\n            return response.text # Return as text if not JSON\n    except requests.exceptions.Timeout:\n        return f'Error: Request timed out for URL: {url}'\n    except requests.exceptions.RequestException as e:\n        return f'Error fetching URL {url}: {e}'\n\n# Example usage:\n# data_json = fetch_url_data('https://jsonplaceholder.typicode.com/todos/1')\n# print(f\"JSON Data: {data_json}\")\n# data_text = fetch_url_data('https://example.com')\n# print(f\"Text Data (first 100 chars): {data_text[:100] if isinstance(data_text, str) else data_text}...\")\n# data_error = fetch_url_data('https://nonexistent-url-for-testing.com/api')\n# print(f\"Error Data: {data_error}\")", "explanation": "Uses `requests.get()` to fetch URL data, with `raise_for_status()` for HTTP error checking, JSON parsing, and timeout/request error handling.", "tags": ["python", "requests", "api", "http get", "fetch data", "json", "error handling", "web"]}, {"description": "Python function to safely read a file, handling FileNotFoundError and other IOErrors.", "example_id": "safe_fileread_errors_007", "text_to_embed": "Python function to safely read a file, handling FileNotFoundError and other IOErrors. Python FileNotFoundError handling, try-except open file, safe file reading. How to handle FileNotFoundError in Python? Python try-except for opening files. Safely open file Python error handling", "ideal_code": "def read_file_safely(filepath):\n    \"\"\"Reads a file and handles FileNotFoundError and other IOErrors.\"\"\"\n    try:\n        with open(filepath, 'r', encoding='utf-8') as file:\n            content = file.read()\n        return content\n    except FileNotFoundError:\n        print(f'Error: The file \"{filepath}\" was not found.')\n        return None\n    except IOError as e:\n        print(f'Error: An IO error occurred while reading \"{filepath}\": {e}')\n        return None\n    except Exception as e:\n        print(f'An unexpected error occurred with \"{filepath}\": {e}')\n        return None\n\n# Example usage:\n# Create a dummy file for testing success case:\n# with open('existing_file.txt', 'w') as f:\n#     f.write('Hello, world!')\n# print(\"--- Reading existing file ---\")\n# content = read_file_safely('existing_file.txt')\n# if content:\n#     print(f\"File content: {content}\")\n# print(\"--- Attempting to read non-existent file ---\")\n# read_file_safely('non_existent_file.txt')\n# import os\n# if os.path.exists('existing_file.txt'): os.remove('existing_file.txt')", "explanation": "Safely reads file content using `try-except` to handle `FileNotFoundError`, `IOError`, and other exceptions. Uses `with` for resource management.", "tags": ["python", "error handling", "filenotfounderror", "try-except", "file io", "safe programming"]}, {"description": "Examples of Python list comprehensions for creating lists, like squaring evens and uppercasing words.", "example_id": "list_comprehension_examples_008", "text_to_embed": "Examples of Python list comprehensions for creating lists, like squaring evens and uppercasing words. Python list comprehension, filter and map list, concise list creation. Python list comprehension example. How to use list comprehension in Python to filter list. Create new list from existing list Python concisely", "ideal_code": "# Square of even numbers from 0 to 9\nnumbers_range = range(10)\neven_squares = [x*x for x in numbers_range if x % 2 == 0]\n# print(f\"Squares of even numbers: {even_squares}\") # Output: [0, 4, 16, 36, 64]\n\n# Convert list of strings to uppercase\nwords = ['hello', 'world', 'python', 'is', 'fun']\nupper_words = [word.upper() for word in words if len(word) > 2]\n# print(f\"Uppercase words (len > 2): {upper_words}\") # Output: ['HELLO', 'WORLD', 'PYTHON', 'FUN']", "explanation": "Demonstrates Python list comprehensions for concisely creating lists, e.g., `[x*x for x in range(10) if x % 2 == 0]` to square even numbers.", "tags": ["python", "list comprehension", "data manipulation", "filter", "map", "concise code"]}, {"description": "Explanation of differences between `var`, `let`, and `const` for variable declaration in JavaScript, including scope and hoisting.", "example_id": "js_var_let_const_diff_009", "text_to_embed": "Explanation of differences between `var`, `let`, and `const` for variable declaration in JavaScript, including scope and hoisting. JavaScript var let const differences, variable scope JS, hoisting temporal dead zone. What's the difference between let, const, and var in JavaScript? JavaScript variable declaration: let vs const vs var. Explain var, let, and const scope in JS", "ideal_code": "// var: function-scoped or globally-scoped, hoisted with undefined\nfunction varExample() {\n  console.log(\"Before var declaration:\", typeof xVar); // undefined (due to hoisting)\n  if (true) { \n    var xVar = 10; \n  }\n  console.log(\"After var declaration (inside function, outside block):\", xVar); // 10 (accessible outside block)\n}\n// varExample();\n// console.log(\"Outside function:\", typeof xVar); // undefined (if varExample() was not called or xVar is not global)\n\n// let: block-scoped, hoisted but not initialized (Temporal Dead Zone - TDZ)\nfunction letExample() {\n  // console.log(yLet); // ReferenceError: Cannot access 'yLet' before initialization (TDZ)\n  if (true) { \n    let yLet = 20; \n    console.log(\"Inside block (let):\", yLet); /* 20 */ \n  }\n  // console.log(\"Outside block (let):\", yLet); // ReferenceError: yLet is not defined\n}\n// letExample();\n\n// const: block-scoped, like let, but cannot be reassigned.\n// Must be initialized during declaration.\nfunction constExample() {\n  const zConst = 30;\n  // zConst = 40; // TypeError: Assignment to constant variable.\n  console.log(\"Const variable:\", zConst); // 30\n\n  const objConst = { a: 1 };\n  objConst.a = 2; // This is allowed, as we are mutating the object's property, not reassigning 'objConst'\n  console.log(\"Mutated const object property:\", objConst.a); // 2\n  // objConst = { b: 3 }; // TypeError: Assignment to constant variable.\n}\n// constExample();", "explanation": "`var` is function-scoped and hoisted. `let` and `const` are block-scoped, hoisted but in TDZ; `const` cannot be reassigned. Prefer `const`, then `let`.", "tags": ["javascript", "variables", "var", "let", "const", "scope", "hoisting", "tdz", "es6"]}, {"description": "Demonstrates using Python's `enumerate()` function to iterate over a list with both index and value.", "example_id": "python_enumerate_list_010", "text_to_embed": "Demonstrates using Python's `enumerate()` function to iterate over a list with both index and value. Python enumerate list index value, iterate with index, Pythonic loop access item and position. How to iterate over a list and get both index and value in Python? Pythonic way to get index and item in loop. Python for loop with index and value", "ideal_code": "my_list = ['apple', 'banana', 'cherry', 'date']\n\n# Using enumerate() (Pythonic way)\nprint('--- Using enumerate ---')\nfor index, value in enumerate(my_list):\n    print(f'Index: {index}, Value: {value}')\n\n# Using enumerate() with a starting index\nprint('\\n--- Using enumerate with start=1 ---')\nfor index, value in enumerate(my_list, start=1):\n    print(f'Position: {index}, Fruit: {value}')\n\n# For comparison, a less Pythonic way (manual index tracking):\nprint('\\n--- Less Pythonic way (manual index) ---')\nidx = 0\nfor value in my_list:\n    print(f'Index: {idx}, Value: {value}')\n    idx += 1\n\n# Another common but often less readable way for simple iteration with index:\nprint('\\n--- Using range(len()) (can be less Pythonic) ---')\nfor i in range(len(my_list)):\n    print(f'Index: {i}, Value: {my_list[i]}')", "explanation": "Use `enumerate(iterable, start=0)` to get (index, value) pairs while iterating. `start` argument is optional (default 0).", "tags": ["python", "enumerate", "loop", "iteration", "index", "value", "best practice", "pythonic"]}, {"description": "Python function to send a POST request with JSON data using the requests library, with error handling.", "example_id": "requests_post_json_011", "text_to_embed": "Python function to send a POST request with JSON data using the requests library, with error handling. Python requests.post JSON data, send POST with JSON payload, API POST request Python. How do I use requests.post in Python to send JSON data? Python send POST request with JSON payload. requests.post JSON example Python", "ideal_code": "import requests\nimport json # Though not strictly needed if using requests' json parameter\n\ndef post_json_data(url, data_dict):\n    \"\"\"Sends a POST request with JSON data and handles the response.\"\"\"\n    headers = {'Content-Type': 'application/json'} # Good practice, though requests.post(json=...) sets it.\n    try:\n        # The `json` parameter automatically serializes the dict to JSON\n        # and sets the Content-Type header to 'application/json'.\n        response = requests.post(url, json=data_dict, headers=headers, timeout=10)\n        response.raise_for_status() # Check for HTTP errors (4xx or 5xx status codes)\n        \n        # Try to parse the response as JSON, common for APIs\n        try:\n            return response.json()\n        except json.JSONDecodeError: # Or requests.exceptions.JSONDecodeError\n            # If response is not JSON, return its text content\n            print(\"Response was not JSON, returning text.\")\n            return response.text\n            \n    except requests.exceptions.Timeout:\n        print(f'Error: Request timed out for POST to {url}')\n        return None\n    except requests.exceptions.RequestException as e:\n        print(f'Error sending POST request to {url}: {e}')\n        return None\n\n# Example usage:\n# api_url = 'https://jsonplaceholder.typicode.com/posts'\n# new_post_data = {\n#     'title': 'My New Post',\n#     'body': 'This is the content of my new post.',\n#     'userId': 101\n# }\n# response_data = post_json_data(api_url, new_post_data)\n# if response_data:\n#     print('Successfully created post (or got response):')\n#     print(response_data)", "explanation": "Uses `requests.post(url, json=data_dict)` to send JSON. Includes `raise_for_status()` for HTTP errors and comprehensive error handling for timeouts/requests.", "tags": ["python", "requests", "api", "http post", "json", "send data", "error handling", "web"]}, {"description": "Explains common causes of TypeError in Python (e.g., operand mismatch, calling non-callable) and how to debug.", "example_id": "debug_typeerror_python_012", "text_to_embed": "Explains common causes of TypeError in Python (e.g., operand mismatch, calling non-callable) and how to debug. Python TypeError explanation, debug unsupported operand type, 'int' object is not callable fix. Why am I getting a TypeError in Python? Python TypeError: unsupported operand type(s). How to debug TypeError in Python concatenation or function calls", "ideal_code": "# Scenario 1: Unsupported operand type(s) (e.g., string + integer)\n# age_val = 30\n# message_wrong = \"My age is: \" + age_val  # This will cause: TypeError: can only concatenate str (not \"int\") to str\n\n# Corrected code for Scenario 1:\n# Convert the integer to a string before concatenation\nage_val = 30\nmessage_corrected_str = \"My age is: \" + str(age_val)\n# print(message_corrected_str) # Output: My age is: 30\n\n# Or use an f-string (Python 3.6+), which is often preferred:\nmessage_corrected_fstring = f\"My age is: {age_val}\"\n# print(message_corrected_fstring) # Output: My age is: 30\n\n# Scenario 2: Calling a non-callable object (e.g. an integer as a function)\n# my_variable = 10\n# result = my_variable() # This will cause: TypeError: 'int' object is not callable\n\n# Corrected approach for Scenario 2: Ensure you are calling an actual function/method\ndef my_actual_function():\n    return \"Function was called successfully!\"\n# result_corrected = my_actual_function()\n# print(result_corrected)\n\n# Scenario 3: Incorrect number of arguments to a function\ndef greet(name, greeting):\n    return f\"{greeting}, {name}!\"\n# greet(\"Alice\") # TypeError: greet() missing 1 required positional argument: 'greeting'", "explanation": "`TypeError` occurs from operations on inappropriate types, like `str + int` or calling non-functions. Debug by checking types with `type()` and ensuring correct operations/arguments.", "tags": ["python", "error handling", "debug", "typeerror", "type checking", "concatenation", "callable"]}, {"description": "Suggests refactoring deeply nested Python loops into generator functions for clarity and improved readability.", "example_id": "refactor_nestedloops_generator_013", "text_to_embed": "Suggests refactoring deeply nested Python loops into generator functions for clarity and improved readability. Refactor Python nested loops, improve loop readability, generator for flattening nested lists. How can I make this nested Python loop more readable? Refactor complex nested loops Python. Improve readability of Python nested loops by flattening", "ideal_code": "# Less optimal: Deeply nested loop processing a 3-level structure\n# nested_data_structure = [[(1, 'a'), (2, 'b')], [(3, 'c'), (4, 'd')], [(5,'e')]]\n# processed_items = []\n# for group in nested_data_structure:\n#     for pair in group:\n#         # Assuming pair is a tuple like (number, letter)\n#         number_val = pair[0]\n#         letter_val = pair[1]\n#         processed_items.append(f\"{number_val}-{letter_val.upper()}\")\n# print(f\"Original processed: {processed_items}\")\n\n# Refactored using a generator function for clarity\ndef process_nested_items(nested_structure):\n    \"\"\"Processes items from a 3-level nested structure and yields them.\"\"\"\n    for group in nested_structure:\n        for pair in group:\n            if isinstance(pair, tuple) and len(pair) == 2:\n                number_val, letter_val = pair # Unpack the pair\n                yield f\"{number_val}-{str(letter_val).upper()}\" # Process and yield\n            # else: handle malformed pair data if necessary\n\nnested_data_structure = [[(1, 'a'), (2, 'b')], [(3, 'c'), (4, 'd')], [(5,'e'), (6, 'f')]]\n# print(\"\\nRefactored output using generator:\")\n# refactored_processed_items = list(process_nested_items(nested_data_structure))\n# print(f\"Refactored processed: {refactored_processed_items}\")\n\n# Alternative for simple flattening (if no processing needed during flattening):\n# from itertools import chain\n# simple_2d_nested = [[1, 2], [3, 4], [5, 6]]\n# flattened_simple = list(chain.from_iterable(simple_2d_nested))\n# print(f\"\\nFlattened with itertools.chain: {flattened_simple}\")", "explanation": "Refactor deeply nested loops into generator functions to improve readability, separate concerns, and potentially enhance memory efficiency.", "tags": ["python", "refactoring", "nested loops", "readability", "generators", "itertools", "code quality"]}, {"description": "Python function to create a directory, including parent directories, using `os.makedirs` with `exist_ok=True`.", "example_id": "create_directory_makedirs_014", "text_to_embed": "Python function to create a directory, including parent directories, using `os.makedirs` with `exist_ok=True`. Python create directory, os.makedirs make folder, create nested directories Python if not exist. How to create a directory in Python? Python make new folder if not exists. os.makedirs example Python to create directory tree", "ideal_code": "import os\n\ndef create_directory_if_not_exists(dir_path):\n    \"\"\"Creates a directory, including parent directories if they don't exist.\n    Returns True if directory was created or already exists, False on error.\n    \"\"\"\n    try:\n        # os.makedirs() will create all intermediate directories in the path.\n        # exist_ok=True prevents an OSError if the directory already exists (Python 3.2+).\n        os.makedirs(dir_path, exist_ok=True)\n        print(f\"Directory '{dir_path}' created or already exists.\")\n        return True\n    except OSError as e:\n        # This might catch permission errors or other OS-level issues.\n        print(f\"Error creating directory '{dir_path}': {e}\")\n        return False\n    except Exception as e:\n        print(f\"An unexpected error occurred while creating directory '{dir_path}': {e}\")\n        return False\n\n# Example usage:\n# path1 = 'my_new_folder/my_subfolder/another_level'\n# path2 = 'another_single_folder'\n# path_exists = 'my_new_folder' # Will be created by path1 call if not existing\n\n# print(f\"Creating '{path1}': {create_directory_if_not_exists(path1)}\")\n# print(f\"Creating '{path2}': {create_directory_if_not_exists(path2)}\")\n# print(f\"Creating '{path_exists}' again: {create_directory_if_not_exists(path_exists)}\")\n\n# To clean up (optional):\n# import shutil\n# if os.path.exists('my_new_folder'): shutil.rmtree('my_new_folder')\n# if os.path.exists('another_single_folder'): shutil.rmtree('another_single_folder')", "explanation": "Uses `os.makedirs(dir_path, exist_ok=True)` to create directories, including parents, without error if they already exist. Handles `OSError`.", "tags": ["python", "file system", "os module", "makedirs", "create directory", "folder", "error handling"]}, {"description": "JavaScript function using `fetch` API with `async/await` to get JSON data from a URL, including error handling.", "example_id": "js_fetch_async_await_015", "text_to_embed": "JavaScript function using `fetch` API with `async/await` to get JSON data from a URL, including error handling. JavaScript fetch API GET request, async await fetch example, JS consume REST API. How to use fetch API in JavaScript to get data? JavaScript fetch example GET request with async/await. Async/await with fetch in JS for API calls", "ideal_code": "async function fetchData(url) {\n    console.log(`Fetching data from: ${url}`);\n    try {\n        // Initiate the fetch request. 'await' pauses execution until the promise resolves.\n        const response = await fetch(url, { timeout: 5000 }); // Added timeout (note: native fetch timeout is via AbortController)\n\n        // Check if the request was successful (status code 200-299).\n        if (!response.ok) {\n            // If not successful, throw an error with the status text.\n            // This will be caught by the catch block.\n            throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);\n        }\n\n        // Parse the response body as JSON. 'await' pauses for this promise too.\n        const data = await response.json();\n        console.log('Data received:', data);\n        return data;\n\n    } catch (error) {\n        // Handle any errors that occurred during the fetch or processing.\n        console.error('Error fetching data:', error.message);\n        // You might want to return null or a specific error object here\n        // depending on how the caller needs to handle errors.\n        return null;\n    }\n}\n\n// Example usage:\n// (async () => {\n//     const todoData = await fetchData('https://jsonplaceholder.typicode.com/todos/1');\n//     if (todoData) {\n//         console.log('Fetched Todo Title:', todoData.title);\n//     }\n\n//     console.log('\\n--- Fetching from a non-existent URL ---');\n//     await fetchData('https://jsonplaceholder.typicode.com/nonexistent');\n    \n//     console.log('\\n--- Fetching from a URL that might timeout (requires server setup or use AbortController for real timeout) ---');\n//     // Note: The {timeout: 5000} option is NOT a standard part of the fetch API directly for timeout.\n//     // Proper timeout handling requires AbortController. This is a conceptual placeholder.\n//     // For a real timeout, you'd use: \n//     // const controller = new AbortController();\n//     // const timeoutId = setTimeout(() => controller.abort(), 5000);\n//     // const response = await fetch(url, { signal: controller.signal });\n//     // clearTimeout(timeoutId);\n//     // For simplicity, this example doesn't implement full AbortController logic.\n//     await fetchData('https://deelay.me/7000/https://jsonplaceholder.typicode.com/todos/2'); // Simulates delay\n// })();", "explanation": "Uses `async/await` with `fetch(url)` for GET requests. Checks `response.ok`, parses JSON with `response.json()`, and includes `try/catch` for errors.", "tags": ["javascript", "fetch api", "async", "await", "promise", "http get", "json", "api", "error handling"]}, {"description": "Shows various methods to merge two Python dictionaries, including `update()`, dictionary unpacking, and the `|` operator (Python 3.9+).", "example_id": "python_dict_merge_methods_016", "text_to_embed": "Shows various methods to merge two Python dictionaries, including `update()`, dictionary unpacking, and the `|` operator (Python 3.9+). Python merge dictionaries, combine two dicts, update dictionary keys values Python 3.5 Python 3.9. How to merge two dictionaries in Python? Python combine dictionaries with overwriting keys. Update dictionary with another dictionary Python different versions", "ideal_code": "dict_a = {'name': '<PERSON>', 'age': 30, 'city': 'New York'}\ndict_b = {'age': 31, 'occupation': 'Engineer', 'country': 'USA'}\n\n# Method 1: Using the `update()` method (modifies dict_a_copy in-place)\nprint(\"--- Method 1: update() ---\")\ndict_a_copy_for_update = dict_a.copy() # Use .copy() to preserve original dict_a\ndict_a_copy_for_update.update(dict_b)\n# print(f\"Original dict_a: {dict_a}\")\n# print(f\"Merged with update(): {dict_a_copy_for_update}\") \n# Output: {'name': '<PERSON>', 'age': 31, 'city': 'New York', 'occupation': 'Engineer', 'country': 'USA'}\n\n# Method 2: Using dictionary unpacking `{**d1, **d2}` (Python 3.5+)\n# Creates a new dictionary. If keys overlap, value from the rightmost dict (dict_b) wins.\nprint(\"\\n--- Method 2: Dictionary Unpacking (Python 3.5+) ---\")\nmerged_dict_unpack = {**dict_a, **dict_b}\n# print(f\"Merged with unpacking: {merged_dict_unpack}\")\n# Output: {'name': '<PERSON>', 'age': 31, 'city': 'New York', 'occupation': 'Engineer', 'country': 'USA'}\n\n# Method 3: Using the | merge operator (Python 3.9+)\n# Creates a new dictionary. If keys overlap, value from the rightmost dict (dict_b) wins.\nprint(\"\\n--- Method 3: Merge Operator | (Python 3.9+) ---\")\n# This code will only run on Python 3.9+\nimport sys\nif sys.version_info >= (3, 9):\n    merged_dict_pipe = dict_a | dict_b\n    # print(f\"Merged with | operator: {merged_dict_pipe}\")\n    # In-place merge with |= (also Python 3.9+)\n    # dict_a_copy_for_pipe_update = dict_a.copy()\n    # dict_a_copy_for_pipe_update |= dict_b\n    # print(f\"Merged in-place with |= : {dict_a_copy_for_pipe_update}\")\nelse:\n    # print(\"Merge operator | and |= require Python 3.9+\")\n    pass", "explanation": "Merges dictionaries using `dict.update()` (in-place), `{**d1, **d2}` (new dict, Py3.5+), or `d1 | d2` (new dict, Py3.9+). Later values overwrite earlier ones for same keys.", "tags": ["python", "dictionary", "merge", "combine", "update", "unpacking", "pipe operator", "dict"]}, {"description": "Detailed breakdown of a Python list comprehension that squares odd numbers within a range.", "example_id": "codeexplain_listcomp_sq_odd_017", "text_to_embed": "Detailed breakdown of a Python list comprehension that squares odd numbers within a range. Explain Python list comprehension for squares of odd numbers, code breakdown range and if condition. Explain this Python code: `squares = [x**2 for x in range(5) if x % 2 != 0]`. What does this list comprehension do in detail? Breakdown Python list comprehension: `[x**2 for x in range(5) if x % 2 != 0]`", "ideal_code": "# The Python code snippet to explain:\n# squares_of_odds = [x**2 for x in range(5) if x % 2 != 0]\n# print(squares_of_odds) # Expected output: [1, 9]", "explanation": "`[x**2 for x in range(5) if x % 2 != 0]` iterates `x` from 0-4, filters for odd `x`, then squares `x`, collecting results `[1, 9]` in a list.", "tags": ["python", "list comprehension", "code explanation", "for loop", "if condition", "range", "syntax breakdown"]}, {"description": "Explains the Python Global Interpreter Lock (GIL), its effects on multithreading for CPU-bound vs. I/O-bound tasks, and workarounds.", "example_id": "python_gil_explanation_018", "text_to_embed": "Explains the Python Global Interpreter Lock (GIL), its effects on multithreading for CPU-bound vs. I/O-bound tasks, and workarounds. Python Global Interpreter Lock GIL explained, GIL multithreading impact, CPython GIL limitations and workarounds. What is the Global Interpreter Lock (GIL) in Python? Explain Python GIL and its implications. How does GIL affect Python multithreading performance?", "ideal_code": "// No specific executable code, this is a conceptual explanation.\n// Relevant modules for workarounds: multiprocessing, threading, asyncio\n\n/*\nimport threading\nimport time\n\n# Example of CPU-bound task (GIL limits parallelism)\ndef cpu_bound_task(n):\n    count = 0\n    for i in range(n):\n        count += i\n    # print(f\"Thread {threading.current_thread().name} finished CPU task\")\n\n# Example of I/O-bound task (GIL released, threading helps)\ndef io_bound_task(url):\n    # Simulating a network request\n    # print(f\"Thread {threading.current_thread().name} starting I/O task for {url}\")\n    time.sleep(2) # Represents I/O operation where GIL can be released\n    # print(f\"Thread {threading.current_thread().name} finished I/O task for {url}\")\n*/", "explanation": "The GIL in CPython allows only one thread to execute Python bytecode at a time, limiting CPU-bound parallelism in `threading`. I/O-bound tasks benefit more. Use `multiprocessing` for CPU parallelism.", "tags": ["python", "gil", "global interpreter lock", "multithreading", "concurrency", "parallelism", "cpu-bound", "io-bound", "cpython", "multiprocessing", "asyncio"]}, {"description": "Python implementation of the Bubble Sort algorithm with an optimization for early termination if sorted.", "example_id": "bubblesort_optimized_019", "text_to_embed": "Python implementation of the Bubble Sort algorithm with an optimization for early termination if sorted. Python bubble sort implementation, simple sorting algorithm, O(n^2) sort example with optimization. Implement bubble sort in Python with optimization. Python bubble sort algorithm example. Simple sorting algorithm Python: bubble sort explained", "ideal_code": "def bubble_sort(arr):\n    \"\"\"Sorts a list in ascending order using the Bubble Sort algorithm.\n    Includes an optimization to stop early if the list is already sorted.\n    \"\"\"\n    n = len(arr)\n    if n <= 1:\n        return arr # Already sorted or empty\n\n    for i in range(n - 1): # Outer loop for passes\n        swapped_in_pass = False # Flag to track if any swaps occurred in this pass\n        # Inner loop for comparisons and swaps\n        # Last i elements are already in place, so range is n-i-1\n        for j in range(0, n - i - 1):\n            # Compare adjacent elements\n            if arr[j] > arr[j+1]:\n                # Swap if elements are in the wrong order\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n                swapped_in_pass = True\n        \n        # Optimization: If no two elements were swapped in the inner loop,\n        # then the array is sorted, and we can break early.\n        if not swapped_in_pass:\n            # print(f\"Optimization: Array sorted after pass {i+1}\")\n            break\n    return arr\n\n# Example usage:\n# my_array_unsorted = [64, 34, 25, 12, 22, 11, 90, 5]\n# print(f\"Unsorted array: {my_array_unsorted}\")\n# sorted_array = bubble_sort(my_array_unsorted.copy()) # Sort a copy to keep original\n# print(f\"Sorted array: {sorted_array}\")\n\n# Example with an already sorted array to show optimization\n# already_sorted = [1, 2, 3, 4, 5]\n# print(f\"\\nUnsorted (already sorted) array: {already_sorted}\")\n# bubble_sort(already_sorted) # Will print optimization message if uncommented\n# print(f\"Sorted array: {already_sorted}\")", "explanation": "Implements Bubble Sort by repeatedly swapping adjacent elements if out of order. Optimized to stop early if a pass makes no swaps. O(n^2) average/worst time.", "tags": ["python", "algorithm", "sorting", "bubble sort", "comparison sort", "in-place sort", "optimization", "o(n^2)"]}, {"description": "Python function to write a dictionary to a JSON file using `json.dump`, with options for encoding and indentation.", "example_id": "write_dict_to_json_020", "text_to_embed": "Python function to write a dictionary to a JSON file using `json.dump`, with options for encoding and indentation. Python write dictionary to JSON file, save data as JSON, json.dump example, pretty print JSON to file. How to write a Python dictionary to a JSON file? Python save dict as JSON file with formatting. Example of json.dump in Python to file", "ideal_code": "import json\nimport os\n\ndef write_dict_to_json_file(data_dict, file_path, indent=4):\n    \"\"\"Writes a Python dictionary to a JSON file with specified indentation.\n    Returns True on success, False on error.\n    \"\"\"\n    try:\n        # Ensure the directory for the file_path exists, if not, create it.\n        # This is an optional enhancement for robustness.\n        # directory = os.path.dirname(file_path)\n        # if directory and not os.path.exists(directory):\n        #     os.makedirs(directory, exist_ok=True)\n\n        with open(file_path, 'w', encoding='utf-8') as json_file:\n            # json.dump() serializes the Python dictionary to a JSON formatted string\n            # and writes it to the file object.\n            # ensure_ascii=False: Allows non-ASCII characters (e.g., accented letters, emojis)\n            #                       to be written as they are, rather than as \\uXXXX escapes.\n            #                       This is generally good for UTF-8 encoded files.\n            # indent: If indent is a non-negative integer or string, then JSON array elements\n            #         and object members will be pretty-printed with that indent level.\n            json.dump(data_dict, json_file, ensure_ascii=False, indent=indent)\n        print(f\"Successfully wrote data to '{file_path}'\")\n        return True\n    except IOError as e:\n        print(f\"IOError writing to JSON file '{file_path}': {e}\")\n        return False\n    except TypeError as e:\n        # This can happen if data_dict contains non-JSON-serializable types\n        # (e.g., sets, custom objects without a JSON representation).\n        print(f\"TypeError serializing data to JSON for '{file_path}': {e}. Ensure data is JSON serializable.\")\n        return False\n    except Exception as e:\n        print(f\"An unexpected error occurred while writing to '{file_path}': {e}\")\n        return False\n\n# Example usage:\n# my_sample_data = {\n#     \"name\": \"Viber Assistant\",\n#     \"version\": 1.1,\n#     \"features\": [\"code generation\", \"explanation\", \"refactoring\"],\n#     \"is_active\": True,\n#     \"settings\": None,\n#     \"author_details\": {\"name\": \"AI User\", \"contact\": \"<EMAIL>\"},\n#     \"unicode_test\": \"Accénted charactèrs & emojis 👍\"\n# }\n\n# output_filename = 'output_data.json'\n# if write_dict_to_json_file(my_sample_data, output_filename, indent=2):\n#     print(f\"Check the file '{output_filename}' for the JSON output.\")\n# else:\n#     print(f\"Failed to write JSON data to '{output_filename}'.\")\n\n# To verify (optional):\n# try:\n#     if os.path.exists(output_filename):\n#         with open(output_filename, 'r', encoding='utf-8') as f_read:\n#             print(\"\\nFile content:\")\n#             print(f_read.read())\n#         # os.remove(output_filename) # Clean up\n# except Exception as e:\n#     print(f\"Error reading or cleaning up file: {e}\")", "explanation": "Writes a Python dictionary to a JSON file using `json.dump(data, file_obj, indent=4, ensure_ascii=False)`. Handles `IOError` and `TypeError`.", "tags": ["python", "json", "write file", "save data", "dictionary to json", "json.dump", "file system", "serialization", "pretty print"]}, {"description": "JavaScript examples using `array.map()` to transform elements and `array.filter()` to select elements, including chaining.", "example_id": "js_array_map_filter_chain_021", "text_to_embed": "JavaScript examples using `array.map()` to transform elements and `array.filter()` to select elements, including chaining. JavaScript array map filter methods, functional programming JS arrays, transform and select array elements. How to use map and filter on arrays in JavaScript? JavaScript array.map() example for transformation. JavaScript array.filter() example for selection. Functional array methods in JS: map, filter, and chaining", "ideal_code": "const numbersArray = [1, 2, 3, 4, 5, 6, 7, 8];\n\n// --- Array.prototype.map() ---\n// map: Creates a new array by applying a callback function to each element of the original array.\n// The original array is not modified.\nconst doubledNumbersArray = numbersArray.map(num => num * 2);\n// console.log('Original numbers:', numbersArray);\n// console.log('Doubled numbers (map):', doubledNumbersArray); // Output: [2, 4, 6, 8, 10, 12, 14, 16]\n\nconst numberObjects = numbersArray.map(num => ({ value: num, isEven: num % 2 === 0 }));\n// console.log('Number objects (map):', numberObjects);\n/* Output:\n[ \n  { value: 1, isEven: false }, { value: 2, isEven: true }, \n  { value: 3, isEven: false }, { value: 4, isEven: true }, ... \n]*/\n\n// --- Array.prototype.filter() ---\n// filter: Creates a new array with all elements that pass the test (return true) \n// implemented by the provided callback function. The original array is not modified.\nconst evenNumbersArray = numbersArray.filter(num => num % 2 === 0);\n// console.log('Even numbers (filter):', evenNumbersArray); // Output: [2, 4, 6, 8]\n\nconst numbersGreaterThanFour = numbersArray.filter(num => num > 4);\n// console.log('Numbers > 4 (filter):', numbersGreaterThanFour); // Output: [5, 6, 7, 8]\n\n// --- Chaining map() and filter() ---\n// Get the squares of only the odd numbers\nconst squaresOfOddNumbers = numbersArray\n    .filter(num => num % 2 !== 0)  // First, filter to get odd numbers: [1, 3, 5, 7]\n    .map(oddNum => oddNum * oddNum); // Then, map to get their squares: [1, 9, 25, 49]\n// console.log('Squares of odd numbers (filter then map):', squaresOfOddNumbers);\n\nconst users = [\n    { id: 1, name: 'Alice', age: 30, isActive: true },\n    { id: 2, name: 'Bob', age: 24, isActive: false },\n    { id: 3, name: 'Charlie', age: 35, isActive: true },\n    { id: 4, name: 'Diana', age: 28, isActive: true }\n];\n\n// Get names of active users older than 25\nconst activeSeniorUserNames = users\n    .filter(user => user.isActive && user.age > 25)\n    .map(user => user.name);\n// console.log('Active users > 25 (names only):', activeSeniorUserNames); // Output: ['Alice', 'Charlie', 'Diana']", "explanation": "`array.map(fn)` transforms each element into a new array. `array.filter(fn)` creates a new array with elements passing the test. They can be chained.", "tags": ["javascript", "array methods", "map", "filter", "functional programming", "data transformation", "chaining", "es6", "collections"]}, {"description": "Python examples using the `re` module for regular expression searching (`re.search`) and finding all matches (`re.findall`).", "example_id": "python_regex_search_findall_022", "text_to_embed": "Python examples using the `re` module for regular expression searching (`re.search`) and finding all matches (`re.findall`). Python regex search re.search example, find pattern string regex, re.findall Python email regex. How to use regex for string searching in Python? Python re.search() example to find first match. Find all occurrences of a pattern in a string Python regex using re.findall(). Python regex for email validation or extraction", "ideal_code": "import re\n\nsample_text = \"The quick brown fox jumps over the lazy dog. Contact alice@example.<NAME_EMAIL> for details.\"\n\n# --- Example 1: Using re.search() to find the first occurrence --- \n# re.search(pattern, string) scans string for the first location where pattern produces a match.\n# Returns a match object if found, else None.\n\n# Find the first occurrence of 'fox' (case-sensitive)\nmatch_fox = re.search(r'fox', sample_text)\nif match_fox:\n    # print(f\"Found '{match_fox.group(0)}' starting at index {match_fox.start()} and ending at {match_fox.end()}.\")\n    pass # Output: Found 'fox' starting at index 16 and ending at 19.\nelse:\n    # print(\"'fox' not found.\")\n    pass\n\n# Find the first word starting with 'l' (case-insensitive)\nmatch_l_word = re.search(r'\\bl\\w*', sample_text, re.IGNORECASE) # \\b for word boundary, \\w* for zero or more word characters\nif match_l_word:\n    # print(f\"First word starting with 'l' (case-insensitive): '{match_l_word.group(0)}'\")\n    pass # Output: First word starting with 'l' (case-insensitive): 'lazy'\n\n# --- Example 2: Using re.findall() to find all non-overlapping matches --- \n# re.findall(pattern, string) returns all non-overlapping matches of pattern in string as a list of strings.\n\n# Find all words containing 'o'\nwords_with_o = re.findall(r'\\b\\w*o\\w*\\b', sample_text) # \\b ensures whole words\n# print(f\"Words containing 'o': {words_with_o}\") \n# Output: Words containing 'o': ['brown', 'fox', 'over', 'dog', 'Contact', 'or', 'bob'] (depends on exact \\w definition)\n\n# Find all email addresses\n# A more robust email regex can be very complex, this is a common simplified one.\nemail_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}'\nall_emails = re.findall(email_pattern, sample_text)\n# print(f\"Found email addresses: {all_emails}\")\n# Output: Found email addresses: ['<EMAIL>', '<EMAIL>']\n\n# --- Example 3: Using re.match() to match only at the beginning of the string --- \n# re.match(pattern, string) tries to apply the pattern at the start of the string.\nmatch_beginning = re.match(r'The', sample_text)\nif match_beginning:\n    # print(f\"Pattern 'The' found at the beginning: '{match_beginning.group(0)}'\")\n    pass\nelse:\n    # print(\"'The' not found at the beginning.\")\n    pass\n\nmatch_fox_at_beginning = re.match(r'fox', sample_text)\n# print(f\"Match 'fox' at beginning: {match_fox_at_beginning}\") # Output: None, because 'fox' is not at the start.", "explanation": "`re.search(pattern, string)` finds first match (Match object or None). `re.findall(pattern, string)` finds all non-overlapping matches (list of strings). `re.match()` checks only at string start.", "tags": ["python", "regex", "regular expressions", "re module", "re.search", "re.findall", "re.match", "pattern matching", "text processing", "string search", "email regex"]}, {"description": "Refactoring a Python loop that filters and transforms numbers into a more concise list comprehension.", "example_id": "refactor_loop_to_listcomp_A02", "text_to_embed": "Refactoring a Python loop that filters and transforms numbers into a more concise list comprehension. Refactor Python loop to list comprehension, make loop more readable, improve Python loop for filtering and mapping. How to make this Python loop with if and append more readable? Refactor Python for loop into list comprehension. Improve Python loop code for creating a new list", "ideal_code": "# Original code (less Pythonic for this task):\n# numbers_list = [1, 2, 3, 4, 5, 6, 7, 8]\n# doubled_evens_loop = []\n# for num in numbers_list:\n#     if num % 2 == 0: # Filter for even numbers\n#         doubled_evens_loop.append(num * 2) # Transform and append\n# print(f\"Using a loop: {doubled_evens_loop}\")\n\n# Refactored code using a list comprehension:\ndef double_evens_comprehension(numbers):\n    \"\"\"Doubles even numbers from a list using a list comprehension.\"\"\"\n    if not all(isinstance(n, (int, float)) for n in numbers):\n        raise TypeError(\"All elements in the list must be numbers.\")\n    return [num * 2 for num in numbers if num % 2 == 0]\n\n# Example usage:\n# nums_input = [1, 2, 3, 4, 5, 6, 7, 8]\n# try:\n#     doubled_evens_comp = double_evens_comprehension(nums_input)\n#     print(f\"Using list comprehension: {doubled_evens_comp}\")  # Output: [4, 8, 12, 16]\n#     # print(double_evens_comprehension([1, 2, 'a'])) # Raises TypeError\n# except TypeError as e:\n#     print(f\"Error: {e}\")", "explanation": "Refactors a `for` loop with `if` and `append` into a concise list comprehension `[expr for item in iterable if condition]` for better readability and Pythonic style.", "tags": ["python", "refactoring", "list comprehension", "for loop", "conciseness", "readability", "pythonic", "filter", "map"]}, {"description": "Python `BankAccount` class with methods for deposit, withdrawal, and balance checking, including basic validation.", "example_id": "bank_account_class_oop_012", "text_to_embed": "Python `BankAccount` class with methods for deposit, withdrawal, and balance checking, including basic validation. Python bank account class, OOP deposit withdraw methods, simple bank account example with validation. Create a Python class for a bank account. Python class with deposit and withdraw methods and balance. Simple OOP bank account example in Python", "ideal_code": "class BankAccount:\n    \"\"\"A simple class representing a bank account.\"\"\"\n    def __init__(self, owner_name, initial_balance=0.0):\n        if not isinstance(owner_name, str) or not owner_name.strip():\n            raise ValueError(\"Owner name must be a non-empty string.\")\n        if not isinstance(initial_balance, (int, float)) or initial_balance < 0:\n            raise ValueError(\"Initial balance must be a non-negative number.\")\n        \n        self.owner_name = owner_name\n        self._balance = float(initial_balance) # Use a leading underscore for 'private' attribute\n\n    def deposit(self, amount):\n        if not isinstance(amount, (int, float)) or amount <= 0:\n            print(\"Deposit amount must be a positive number.\")\n            return False\n        self._balance += float(amount)\n        print(f\"Deposited ${amount:.2f}. New balance: ${self._balance:.2f}\")\n        return True\n\n    def withdraw(self, amount):\n        if not isinstance(amount, (int, float)) or amount <= 0:\n            print(\"Withdrawal amount must be a positive number.\")\n            return False\n        if amount > self._balance:\n            print(f\"Withdrawal failed: Insufficient funds. Current balance: ${self._balance:.2f}\")\n            return False\n        self._balance -= float(amount)\n        print(f\"Withdrew ${amount:.2f}. New balance: ${self._balance:.2f}\")\n        return True\n\n    def get_balance(self):\n        return self._balance\n\n    def __str__(self):\n        return f\"Account Owner: {self.owner_name}, Balance: ${self._balance:.2f}\"\n\n# Example usage:\n# try:\n#     account1 = BankAccount('Alice Wonderland', 100.50)\n#     print(account1)\n\n#     account1.deposit(50.25)\n#     account1.withdraw(30)\n#     account1.withdraw(150) # Attempt to overdraw\n#     account1.deposit(-10) # Attempt invalid deposit\n\n#     print(f\"Final balance for {account1.owner_name}: ${account1.get_balance():.2f}\")\n\n#     # account2 = BankAccount('', 200) # Raises ValueError\n#     # account3 = BankAccount('Bob The Builder', -50) # Raises ValueError\n# except ValueError as e:\n#     print(f\"Error creating account: {e}\")", "explanation": "Models a `BankAccount` with `__init__` (validates owner/balance), `deposit`, `withdraw` (validates amount, checks funds), and `get_balance` methods.", "tags": ["python", "class", "oop", "object-oriented", "bank account", "deposit", "withdraw", "balance", "encapsulation", "validation"]}, {"description": "Python function demonstrating an authenticated GET request using a Bear<PERSON> token in the Authorization header with the `requests` library.", "example_id": "requests_auth_bearer_token_014", "text_to_embed": "Python function demonstrating an authenticated GET request using a Bearer token in the Authorization header with the `requests` library. Authenticated GET request in Python with Bearer token, use API key in Authorization header, fetch data with authentication requests. How to make an authenticated GET request in Python with a Bearer token? Python requests with API key in Authorization header. Fetch data from a secure API with authentication in Python", "ideal_code": "import requests\nimport os # For potentially getting API key from environment variable\n\ndef fetch_secure_data_with_bearer_token(api_url, api_key):\n    \"\"\"Fetches data from a secure API using Bearer Token authentication.\"\"\"\n    if not api_key:\n        print(\"Error: API key is missing.\")\n        return None\n\n    headers = {\n        'Authorization': f'Bearer {api_key}',\n        'Content-Type': 'application/json' # Often good to include, though may not be strictly necessary for all GETs\n    }\n    \n    try:\n        response = requests.get(api_url, headers=headers, timeout=10)\n        response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)\n        return response.json() # Assuming the API returns JSON\n    except requests.exceptions.HTTPError as http_err:\n        print(f'HTTP error occurred: {http_err} - Status: {response.status_code}')\n        # You might want to inspect response.text or response.json() for error details from API\n        # try:\n        #     print(f\"API Error Details: {response.json()}\")\n        # except requests.exceptions.JSONDecodeError:\n        #     print(f\"API Error Details (text): {response.text}\")\n        return None\n    except requests.exceptions.Timeout:\n        print(f'Error: Request timed out for URL: {api_url}')\n        return None\n    except requests.exceptions.RequestException as req_err:\n        print(f'A request error occurred: {req_err}')\n        return None\n\n# Example Usage (replace with a real API and key for testing):\n# SECURE_API_ENDPOINT = 'https://api.example.com/v1/data' # Replace with actual API endpoint\n# MY_API_KEY = os.getenv('MY_API_KEY') or 'your_hardcoded_api_key_for_testing_only'\n\n# if not MY_API_KEY or MY_API_KEY == 'your_hardcoded_api_key_for_testing_only':\n#     print(\"Please set a valid API key for testing (e.g., via environment variable MY_API_KEY).\")\n# else:\n#     data = fetch_secure_data_with_bearer_token(SECURE_API_ENDPOINT, MY_API_KEY)\n#     if data:\n#         print(\"Successfully fetched secure data:\")\n#         print(data)\n#     else:\n#         print(\"Failed to fetch secure data.\")", "explanation": "Sends GET request with `Authorization: Bearer {token}` header using `requests`. Includes `raise_for_status()` and error handling for HTTP/network issues.", "tags": ["python", "requests", "api", "authentication", "bearer token", "authorization header", "http get", "secure api", "api key", "error handling"]}, {"description": "Explains a simple Python `for` loop using `range(3)` to print multiples of a number.", "example_id": "codeexplain_for_loop_range_018", "text_to_embed": "Explains a simple Python `for` loop using `range(3)` to print multiples of a number. Explain Python for loop with range, what does range(3) loop do, loop output in Python for printing multiples. What does this Python code do? `for i in range(3): print(i * 2)`. Explain Python for loop with `range()` function. What is the output of this Python loop code example?", "ideal_code": "# The Python code snippet to explain:\n# for i in range(3):\n#     print(i * 2)\n\n# Expected output:\n# 0\n# 2\n# 4", "explanation": "`for i in range(3): print(i * 2)` loops `i` through 0, 1, 2. In each iteration, it prints `i` multiplied by 2, resulting in outputs 0, 2, 4.", "tags": ["python", "for loop", "range function", "iteration", "code explanation", "print", "sequence", "control flow"]}, {"description": "Demonstrates refactoring repetitive Python code (e.g., printing uppercased strings) into a reusable function to follow the DRY principle.", "example_id": "refactor_dry_function_019", "text_to_embed": "Demonstrates refactoring repetitive Python code (e.g., printing uppercased strings) into a reusable function to follow the DRY principle. Remove duplicate code in Python by creating function, refactor repeated Python code into a function, DRY principle example. How to remove duplicate code in Python? Refactor repeated Python code blocks into a reusable function. Improve Python code with less repetition by applying DRY principle", "ideal_code": "# Original code with repetition:\n# print(\"--- Original Code ---\")\n# text_message1 = 'hello python world'\n# processed_text1 = text_message1.upper()\n# print(f\"Original: '{text_message1}', Processed: '{processed_text1}'\")\n\n# text_message2 = 'another example string'\n# processed_text2 = text_message2.upper()\n# print(f\"Original: '{text_message2}', Processed: '{processed_text2}'\")\n\n# text_message3 = 'dry principle'\n# processed_text3 = text_message3.upper()\n# print(f\"Original: '{text_message3}', Processed: '{processed_text3}'\")\n\n# Refactored code using a reusable function:\ndef process_and_print_string(text):\n    \"\"\"Converts a string to uppercase and prints original and processed versions.\"\"\"\n    if not isinstance(text, str):\n        print(\"Error: Input must be a string.\")\n        return\n    processed_text = text.upper()\n    print(f\"Original: '{text}', Processed: '{processed_text}'\")\n\n# Example usage of the refactored function:\n# print(\"\\n--- Refactored Code ---   \")\n# process_and_print_string('hello python world')\n# process_and_print_string('another example string')\n# process_and_print_string('dry principle')\n# process_and_print_string(123) # Example of error handling", "explanation": "Refactors repetitive code by extracting common logic into a reusable function, adhering to the DRY (Don't Repeat Yourself) principle for better maintainability.", "tags": ["python", "refactoring", "dry principle", "code duplication", "functions", "reusability", "maintainability", "code quality"]}, {"description": "Java example explaining `NullPointerException` when accessing uninitialized array elements or null objects, and how to prevent it with checks.", "example_id": "java_nullpointerexception_debug_020", "text_to_embed": "Java example explaining `NullPointerException` when accessing uninitialized array elements or null objects, and how to prevent it with checks. Fix NullPointerException in Java array access, why am I getting NullPointerException in Java object method call, debug Java array null error safe access. How to fix NullPointerException in Java when accessing array elements? Why am I getting NullPointerException in Java on an object method call? Debug Java array null error and provide safe access example", "ideal_code": "public class NullPointerExample {\n\n    public static void main(String[] args) {\n        // Scenario 1: Accessing an element of a null array reference\n        String[] nullArray = null;\n        try {\n            // System.out.println(nullArray[0]); // This would cause NullPointerException\n        } catch (NullPointerException e) {\n            System.out.println(\"Caught NPE (Scenario 1): Cannot access element of a null array. \" + e.getMessage());\n        }\n\n        // Scenario 2: Accessing a method/field of a null object within an initialized array\n        String[] stringArray = new String[3]; // Array is initialized, but elements are null by default for objects\n        // stringArray[0] is currently null\n        try {\n            // System.out.println(stringArray[0].length()); // This causes NullPointerException because stringArray[0] is null\n        } catch (NullPointerException e) {\n            System.out.println(\"Caught NPE (Scenario 2): Attempted to call method on a null object in array. \" + e.getMessage());\n        }\n        \n        // Corrected / Safe Access for Scenario 2:\n        stringArray[0] = \"Hello\"; // Initialize the element\n        if (stringArray[0] != null) {\n            System.out.println(\"Length of stringArray[0]: \" + stringArray[0].length()); // Safe access\n        } else {\n            System.out.println(\"stringArray[0] is null, cannot get length.\");\n        }\n\n        // Scenario 3: Calling a method on a null object reference\n        MyCustomObject myObject = null;\n        try {\n            // myObject.doSomething(); // This would cause NullPointerException\n        } catch (NullPointerException e) {\n            System.out.println(\"Caught NPE (Scenario 3): Attempted to call method on a null object reference. \" + e.getMessage());\n        }\n        \n        // Corrected / Safe Access for Scenario 3:\n        myObject = new MyCustomObject(); // Initialize the object\n        if (myObject != null) {\n            myObject.doSomething(); // Safe access\n        } else {\n            System.out.println(\"myObject is null, cannot call doSomething().\");\n        }\n    }\n\n    // Dummy class for Scenario 3\n    static class MyCustomObject {\n        public void doSomething() {\n            System.out.println(\"MyCustomObject is doing something!\");\n        }\n    }\n}", "explanation": "A `NullPointerException` in Java occurs when using a `null` reference as if it's an object. Prevent by checking for `null` before access and ensuring proper initialization.", "tags": ["java", "nullpointerexception", "npe", "error handling", "debugging", "array", "object reference", "null check", "safe programming"]}, {"description": "Python function to write a list of strings to a text file, each string on a new line.", "example_id": "python_write_lines_to_file_N01", "text_to_embed": "Python function to write a list of strings to a text file, each string on a new line. Python write to text file, save list to file, python file output, writing lines to file. How to write text to a file in Python? Python code to save a list of strings to a file, each on a new line. Example of writing multiple lines to a text file in Python", "ideal_code": "import os\n\ndef write_lines_to_file(file_path, lines_to_write):\n    \"\"\"Writes a list of strings to a file, each on a new line.\n    Overwrites the file if it exists, creates it if it doesn't.\n    Returns True on success, False on error.\n    \"\"\"\n    try:\n        with open(file_path, 'w', encoding='utf-8') as f:\n            for line in lines_to_write:\n                f.write(line + '\\n') # Add newline character after each line\n        print(f\"Successfully wrote {len(lines_to_write)} lines to '{file_path}'.\")\n        return True\n    except IOError as e:\n        print(f\"IOError writing to file '{file_path}': {e}\")\n        return False\n    except TypeError as e:\n        print(f\"TypeError: Ensure 'lines_to_write' is a list of strings. Error: {e}\")\n        return False\n    except Exception as e:\n        print(f\"An unexpected error occurred: {e}\")\n        return False\n\n# Example usage:\n# my_lines = [\n#     \"This is the first line.\",\n#     \"This is the second line.\",\n#     \"And a third one for good measure.\",\n#     \"UTF-8 test: <PERSON><PERSON><PERSON><PERSON><PERSON> ñ Ñ ç Ç\"\n# ]\n# output_file = \"my_output_file.txt\"\n\n# if write_lines_to_file(output_file, my_lines):\n#     # Verify content (optional)\n#     try:\n#         with open(output_file, 'r', encoding='utf-8') as f_read:\n#             print(\"\\n--- File Content Verification ---\")\n#             print(f_read.read())\n#         # os.remove(output_file) # Clean up the created file\n#     except Exception as e_read:\n#         print(f\"Error reading verification file: {e_read}\")", "explanation": "Opens a file in write mode (`'w'`) and iterates through a list, writing each string followed by a newline `\\n`. Uses `with` for safe file handling.", "tags": ["python", "file io", "write file", "text file", "file system", "writelines", "string list", "output", "encoding"]}, {"description": "Python function to parse a JSON formatted string into a Python dictionary or list using `json.loads()`.", "example_id": "python_parse_json_string_N02", "text_to_embed": "Python function to parse a JSON formatted string into a Python dictionary or list using `json.loads()`. Python parse JSON string, convert JSON string to dictionary, json.loads example, deserialize JSON Python. How to parse a JSON string in Python? Convert JSON string to Python dictionary or list. Example of json.loads() in Python. Deserialize JSON string to Python object", "ideal_code": "import json\n\ndef parse_json_string(json_string):\n    \"\"\"Parses a JSON formatted string into a Python object (dict or list).\n    Returns the Python object on success, or None on parsing error.\n    \"\"\"\n    if not isinstance(json_string, str):\n        print(\"Error: Input must be a string.\")\n        return None\n    try:\n        # json.loads() deserializes a JSON string to a Python object.\n        # 's' in loads stands for 'string'.\n        python_object = json.loads(json_string)\n        print(\"Successfully parsed JSON string.\")\n        return python_object\n    except json.JSONDecodeError as e:\n        # This error is raised if the string is not valid JSON.\n        print(f\"JSONDecodeError: Invalid JSON string. Details: {e}\")\n        return None\n    except Exception as e:\n        print(f\"An unexpected error occurred during JSON parsing: {e}\")\n        return None\n\n# Example usage:\n# Valid JSON string (object/dictionary)\n# json_str_dict = '{\"name\": \"Alice\", \"age\": 30, \"isStudent\": false, \"courses\": [\"Math\", \"History\"]}'\n# parsed_dict = parse_json_string(json_str_dict)\n# if parsed_dict:\n#     print(f\"Type: {type(parsed_dict)}, Content: {parsed_dict}\")\n#     print(f\"Name: {parsed_dict.get('name')}\")\n\n# print(\"\\n---\")\n# Valid JSON string (array/list)\n# json_str_array = '[1, \"apple\", {\"color\": \"red\"}, true]'\n# parsed_array = parse_json_string(json_str_array)\n# if parsed_array:\n#     print(f\"Type: {type(parsed_array)}, Content: {parsed_array}\")\n#     if len(parsed_array) > 1: print(f\"Second element: {parsed_array[1]}\")\n\n# print(\"\\n---\")\n# Invalid JSON string (e.g., trailing comma, single quotes for strings)\n# invalid_json_str = '{\"name\": \"Bob\", \"city\": \"London\",}' # Trailing comma usually an error\n# invalid_json_str_single_quotes = \"{'name': 'Charlie'}\" # Single quotes are invalid for JSON keys/strings\n# parse_json_string(invalid_json_str)\n# parse_json_string(invalid_json_str_single_quotes)\n\n# print(\"\\n---\")\n# Non-string input\n# parse_json_string(123)", "explanation": "Uses `json.loads(json_string)` to deserialize a JSON string into a Python dictionary or list. Handles `json.JSONDecodeError` for invalid JSON.", "tags": ["python", "json", "parse json", "json string", "deserialize", "json.loads", "dictionary", "list", "data conversion", "error handling", "jsondecodeerror"]}, {"description": "Python examples for working with dates and times using the `datetime` module, including getting current datetime, formatting, and parsing.", "example_id": "python_datetime_ops_N03", "text_to_embed": "Python examples for working with dates and times using the `datetime` module, including getting current datetime, formatting, and parsing. Python datetime example, get current date time, format datetime to string, parse string to datetime, strftime strptime. How to work with dates and times in Python? Get current date and time in Python and format it. Convert string to datetime object in Python. Python datetime strftime and strptime examples", "ideal_code": "from datetime import datetime, timedelta, timezone\n\n# 1. Get current date and time\nnow_local = datetime.now()  # Current local datetime\nnow_utc = datetime.now(timezone.utc) # Current UTC datetime (timezone-aware)\n\n# print(f\"Current local datetime: {now_local}\")\n# print(f\"Current UTC datetime: {now_utc}\")\n\n# 2. Access specific components\n# print(f\"Year: {now_local.year}, Month: {now_local.month}, Day: {now_local.day}\")\n# print(f\"Hour: {now_local.hour}, Minute: {now_local.minute}, Second: {now_local.second}\")\n# print(f\"Weekday (Monday=0, Sunday=6): {now_local.weekday()}\")\n\n# 3. Formatting datetime objects to strings (strftime - string from time)\n# Common format: YYYY-MM-DD HH:MM:SS\nformatted_string = now_local.strftime(\"%Y-%m-%d %H:%M:%S\")\n# print(f\"Formatted string (local): {formatted_string}\")\n\n# Another format example: Day, Month Date, Year\nfriendly_format = now_local.strftime(\"%A, %B %d, %Y - %I:%M %p\") # %I for 12-hour, %p for AM/PM\n# print(f\"Friendly format (local): {friendly_format}\")\n\n# 4. Parsing strings into datetime objects (strptime - string parse time)\ndate_string_iso = \"2023-10-26T14:30:55\"\ndate_string_custom = \"26/October/2023 02:30 PM\"\n\ntry:\n    dt_object_iso = datetime.fromisoformat(date_string_iso) # For ISO 8601 formats (Python 3.7+)\n    # print(f\"Parsed from ISO string: {dt_object_iso}\")\n    \n    dt_object_custom = datetime.strptime(date_string_custom, \"%d/%B/%Y %I:%M %p\")\n    # print(f\"Parsed from custom string: {dt_object_custom}\")\nexcept ValueError as e:\n    print(f\"Error parsing date string: {e}\")\n\n# 5. Date and time arithmetic using timedelta\none_day = timedelta(days=1)\ntomorrow = now_local + one_day\n# print(f\"Tomorrow (local): {tomorrow.strftime('%Y-%m-%d')}\")\n\nhours_ago = timedelta(hours=3, minutes=30)\npast_time = now_local - hours_ago\n# print(f\"3.5 hours ago (local): {past_time.strftime('%Y-%m-%d %H:%M:%S')}\")\n\n# 6. Creating specific datetime objects\nspecific_date = datetime(year=2024, month=1, day=15, hour=10, minute=0, second=0)\n# print(f\"Specific datetime: {specific_date}\")", "explanation": "Uses `datetime.now()`, `strftime()` for formatting to string, `strptime()`/`fromisoformat()` for parsing from string, and `timedelta` for arithmetic.", "tags": ["python", "datetime", "date", "time", "timezone", "<PERSON><PERSON><PERSON>", "strftime", "strptime", "format date", "parse date", "current time", "utc"]}, {"description": "Python function to check if a key exists in a dictionary using `in` operator or `get()` method.", "example_id": "python_dict_haskey_001", "text_to_embed": "Python check if key exists in dictionary. How to safely access dictionary keys Python. Python 'in' operator for dict keys. dict.get() method Python example. Check for key in Python dictionary before access.", "ideal_code": "my_dict = {'name': '<PERSON>', 'age': 30}\n\ndef check_key_in_dict(d, key_to_check):\n    # Method 1: Using the 'in' operator (preferred for just checking existence)\n    if key_to_check in d:\n        print(f\"'{key_to_check}' exists in dictionary (using 'in'). Value: {d[key_to_check]}\")\n        return True\n    else:\n        print(f\"'{key_to_check}' does not exist in dictionary (using 'in').\")\n        return False\n\ndef get_value_safely(d, key_to_get, default_value=None):\n    # Method 2: Using dict.get() (returns value or default if key not found)\n    value = d.get(key_to_get, default_value)\n    if value is not default_value or key_to_get in d: # Handles case where default_value is actual value\n        print(f\"Value for '{key_to_get}' (using .get()): {value}\")\n    else:\n        print(f\"'{key_to_get}' not found (using .get()), returned default: {default_value}\")\n    return value\n\n# Example Usage:\n# check_key_in_dict(my_dict, 'age')\n# check_key_in_dict(my_dict, 'city')\n\n# get_value_safely(my_dict, 'name')\n# get_value_safely(my_dict, 'occupation', 'N/A')", "explanation": "Use the `in` operator (`key in my_dict`) to check for key existence, or `my_dict.get(key, default)` to safely retrieve a value with a fallback.", "tags": ["python", "dictionary", "key", "exists", "in operator", "get method", "data access"]}, {"description": "JavaScript example for string interpolation using template literals (backticks).", "example_id": "js_template_literals_001", "text_to_embed": "JavaScript string interpolation. How to use template literals in JS. JavaScript backtick strings for variables. JS embed expressions in strings. Format string with variables JavaScript ES6.", "ideal_code": "const name = \"World\";\nconst version = 1.2;\n\n// Using template literals (backticks ``)\nconst greeting = `Hello, ${name}! Welcome to version ${version + 0.3}.`;\nconsole.log(greeting);\n// Output: Hello, <PERSON>! Welcome to version 1.5.\n\nconst multiLineString = `This is a string\nthat spans across\nmultiple lines.`;\nconsole.log(multiLineString);\n\n// Old way (concatenation)\nconst oldGreeting = \"Hello, \" + name + \"! Welcome to version \" + (version + 0.3) + \".\";\nconsole.log(oldGreeting);", "explanation": "Use template literals (strings enclosed in backticks ``) with `${expression}` placeholders for easy string interpolation and multi-line strings in JavaScript (ES6+).", "tags": ["javascript", "es6", "template literals", "string interpolation", "backticks", "strings"]}, {"description": "Conceptual explanation of what an API (Application Programming Interface) is.", "example_id": "concept_whatis_api_001", "text_to_embed": "What is an API? Explain Application Programming Interface. API definition for beginners. How do APIs work? Role of API in software communication. REST API SOAP API.", "ideal_code": "// This is a conceptual explanation, no executable code.\n/*\nAnalogy: Restaurant Menu\n- You (Client Application) want food (Data/Functionality).\n- The Menu (API Documentation) lists what's available and how to order.\n- The Waiter (API) takes your order (Request) to the Kitchen (Server/Service).\n- The Kitchen prepares the food (Processes Request).\n- The Waiter brings your food (Response).\n\nYou don't need to know how the kitchen works, just how to use the menu and talk to the waiter.\n*/", "explanation": "An API (Application Programming Interface) is a contract or set of rules allowing different software applications to communicate and exchange data/functionality without needing to know each other's internal details.", "tags": ["api", "concept", "software development", "integration", "interface", "rest", "soap", "web services"]}, {"description": "Basic SQL SELECT query to retrieve specific columns from a table, with an optional WHERE clause.", "example_id": "sql_select_where_001", "text_to_embed": "SQL SELECT statement example. How to retrieve data from SQL table. SQL query for specific columns. SQL WHERE clause example. Basic SQL data retrieval.", "ideal_code": "/* Example: Selecting specific columns from a 'Employees' table */\nSELECT FirstName, LastName, Email\nFROM Employees;\n\n/* Example: Selecting all columns from 'Products' table for a specific category */\nSELECT *\nFROM Products\nWHERE CategoryID = 101;\n\n/* Example: Selecting distinct City values from 'Customers' table */\nSELECT DISTINCT City\nFROM Customers\nWHERE Country = 'USA';", "explanation": "`SELECT column1, column2 FROM table_name` retrieves specified columns. `SELECT *` gets all columns. `WHERE condition` filters rows based on the condition.", "tags": ["sql", "select", "query", "database", "data retrieval", "where clause", "rdbms"]}]