import chromadb
from chromadb.utils import embedding_functions
from typing import List, Tu<PERSON>, Dict, Any
import numpy as np
import os

from .vector_store_interface import VectorStoreInterface

class ChromaDBStore(VectorStoreInterface):
    """
    An implementation of VectorStoreInterface using ChromaDB.
    """
    def __init__(self, collection_name: str = "viber_chat_history", persist_directory: str = "./viber_chroma_db"):
        """
        Initializes the ChromaDBStore.

        Args:
            collection_name (str): The name of the collection to use/create in ChromaDB.
            persist_directory (str): The directory to persist ChromaDB data.
                                     If None, data will be in-memory only.
        """
        print(f"Initializing ChromaDBStore with collection: '{collection_name}' and persist_directory: '{persist_directory}'")
        if persist_directory and not os.path.exists(persist_directory):
            os.makedirs(persist_directory, exist_ok=True)
            print(f"Created persist directory: {persist_directory}")

        # We are providing our own embeddings, so we don't need Chroma's default embedding function.
        # However, ChromaDB requires an embedding function to be set for a collection,
        # even if we provide embeddings directly. We can use a dummy one or a lightweight one.
        # For simplicity, we'll use a dummy one that does nothing if we always provide embeddings.
        # A more robust way is to use `chromadb.utils.embedding_functions.SentenceTransformerEmbeddingFunction`
        # if we wanted Chroma to handle embedding generation, but we do it externally.
        # For now, we'll rely on providing embeddings directly to `add` and `query`.
        # If `chromadb.__version__` is 0.4.x or later, `ef=None` is not allowed.
        # We can use a placeholder or a specific SentenceTransformer model name if needed.
        # Let's use a placeholder that won't be actively used for embedding generation by Chroma.
        self.dummy_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name='all-MiniLM-L6-v2')

        if persist_directory:
            self.client = chromadb.PersistentClient(path=persist_directory)
        else:
            self.client = chromadb.Client() # In-memory client

        try:
            self.collection = self.client.get_or_create_collection(
                name=collection_name,
                embedding_function=self.dummy_ef # Required, even if we provide embeddings
            )
            print(f"ChromaDB collection '{collection_name}' loaded/created successfully.")
        except Exception as e:
            error_msg = str(e).lower()
            print(f"Error initializing ChromaDB collection '{collection_name}': {e!r}")
            if "database is locked" in error_msg:
                print("Database is locked. Ensure no other process is using it or try restarting.")
            elif "'_type'" in error_msg or "deserialization" in error_msg or "collection" in error_msg and "does not exist" not in error_msg:
                # Heuristic for schema-like or corruption issues
                print(f"Potential schema mismatch or corruption in existing ChromaDB data at '{persist_directory}'.")
                print(f"RECOMMENDATION: Try deleting the directory '{persist_directory}' and restarting the application.")
            else:
                print("An unexpected error occurred during ChromaDB collection initialization.")
            raise
            
    def close(self):
        """
        Attempts to shut down the ChromaDB client and release resources.
        This is particularly useful for persistent clients in testing scenarios.
        """
        print(f"Closing ChromaDB client for collection: '{self.collection.name}'")

    def add_documents(self, documents: List[Dict[str, Any]], embeddings: List[np.ndarray]) -> None:
        """
        Adds documents and their corresponding embeddings to the ChromaDB collection.
        Assumes each document in the list has an 'id' (str) and 'text' (str) field.
        Other fields in the document dictionary will be stored as metadata.
        """
        if not documents or not embeddings or len(documents) != len(embeddings):
            print("Error: Documents or embeddings list is empty, or their lengths do not match.")
            return

        ids = [str(doc['id']) for doc in documents] # ChromaDB IDs must be strings
        texts = [doc['text'] for doc in documents]
        metadatas = [{k: v for k, v in doc.items() if k not in ['id', 'text', 'embedding']} for doc in documents]

        # Convert numpy embeddings to lists if they are not already, as ChromaDB expects lists of floats.
        embeddings_list = [emb.tolist() if isinstance(emb, np.ndarray) else emb for emb in embeddings]

        self.collection.add(
            ids=ids,
            embeddings=embeddings_list,
            metadatas=metadatas,
            documents=texts # Storing the text content directly in ChromaDB as well
        )
        print(f"Added {len(documents)} documents to ChromaDB collection '{self.collection.name}'.")

    def search_similar(self, query_embedding: np.ndarray, top_k: int = 5) -> List[Tuple[Dict[str, Any], float]]:
        """
        Searches for the top_k most similar documents in ChromaDB.
        Returns documents and their distances (lower is more similar for L2/cosine).
        """
        results = self.collection.query(
            query_embeddings=[query_embedding.tolist() if isinstance(query_embedding, np.ndarray) else query_embedding],
            n_results=top_k,
            include=['metadatas', 'documents', 'distances'] # Requesting metadatas, documents, and distances
        )

        # ChromaDB query results are nested lists, one for each query embedding. We sent one.
        retrieved_docs = []
        if results and results['ids'][0]:
            for i in range(len(results['ids'][0])):
                doc_metadata = results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {}
                doc_text = results['documents'][0][i] if results['documents'] and results['documents'][0] else ""
                distance = results['distances'][0][i] if results['distances'] and results['distances'][0] else float('inf')

                # Reconstruct the document dictionary as expected by the interface
                # The original 'id' was stored in metadata if it wasn't the primary ID for Chroma.
                # Or, if we used the original doc['id'] as Chroma's ID, we can retrieve it.
                # For simplicity, we assume metadata contains the original document structure.
                # We also add the 'text' back if it was stored separately.
                full_doc = {"id": results['ids'][0][i], "text": doc_text, **doc_metadata}
                retrieved_docs.append((full_doc, distance))
        return retrieved_docs