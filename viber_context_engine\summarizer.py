import requests
import json
from typing import Optional

OLLAMA_API_URL = "http://localhost:11434/api/generate"
DEFAULT_SUMMARIZER_MODEL = "phi3:mini" # Or gemma:2b, or another suitable small model
DEFAULT_MAX_SUMMARY_TOKENS = 150 # Target token length for summaries

class Summarizer:
    def __init__(self, model_name: str = DEFAULT_SUMMARIZER_MODEL, ollama_url: str = OLLAMA_API_URL):
        self.model_name = model_name
        self.ollama_url = ollama_url
        print(f"Summarizer initialized for model: {self.model_name} at {self.ollama_url}")

    def _build_summarization_prompt(self, text_to_summarize: str, context_hint: Optional[str] = None, target_token_count: int = DEFAULT_MAX_SUMMARY_TOKENS) -> str:
        hint_text = ""
        if context_hint:
            hint_text = f" The text is a {context_hint}."

        # Adjusting prompt to be more direct for summarization
        prompt = (
            f"Concisely summarize the following text.{hint_text} "
            f"The summary should capture the key information and be approximately {target_token_count} tokens long. "
            f"Do not add any preamble like 'Here is a summary:'. Just provide the summary.\n\n"
            f"Text to summarize:\n\"\"\"\n{text_to_summarize}\n\"\"\"\n\nSummary:"
        )
        return prompt

    def summarize_text(self, 
                       text_to_summarize: str, 
                       context_hint: Optional[str] = None, 
                       max_tokens: int = DEFAULT_MAX_SUMMARY_TOKENS,
                       min_length_to_summarize_chars: int = 200) -> Optional[str]:
        """
        Summarizes the given text if it exceeds a minimum character length.

        Args:
            text_to_summarize (str): The text to be summarized.
            context_hint (Optional[str]): A hint about the text's context (e.g., "user query", "tool output").
            max_tokens (int): The desired approximate maximum token length for the summary.
            min_length_to_summarize_chars (int): Minimum character length of the input text to attempt summarization.

        Returns:
            Optional[str]: The summarized text, or the original text if it's too short or summarization fails.
        """
        if not text_to_summarize or len(text_to_summarize) < min_length_to_summarize_chars:
            return text_to_summarize # Return original if too short

        prompt_for_ollama = self._build_summarization_prompt(text_to_summarize, context_hint, max_tokens)
        
        payload = {
            "model": self.model_name,
            "prompt": prompt_for_ollama,
            "stream": False,
            "options": {
                "temperature": 0.3, # Slightly higher than intent, but still focused
                "num_predict": max_tokens + 50 # Allow some buffer for token prediction
            }
        }

        try:
            print(f"Summarizer: Requesting summary for text (first 100 chars): '{text_to_summarize[:100]}...'")
            response = requests.post(self.ollama_url, json=payload, timeout=20) # 20s timeout for summarization
            response.raise_for_status()
            
            response_data = response.json()
            summary = response_data.get("response", "").strip()
            
            if summary:
                print(f"Summarizer: Original length (chars): {len(text_to_summarize)}, Summary length (chars): {len(summary)}")
                # Basic check: if summary is almost as long as original, or very short, maybe return original
                if len(summary) < 0.8 * len(text_to_summarize) and len(summary) > 10:
                    return summary
                else:
                    print("Summarizer: Summary not significantly shorter or too short, returning original text.")
                    return text_to_summarize
            else:
                print("Summarizer: Ollama returned an empty summary. Returning original text.")
                return text_to_summarize # Return original if summary is empty

        except requests.exceptions.RequestException as e:
            print(f"Summarizer: Error communicating with Ollama API at {self.ollama_url}: {e}")
        except json.JSONDecodeError as e:
            print(f"Summarizer: Error decoding Ollama API response: {e}")
        except Exception as e:
            print(f"Summarizer: Unexpected error during summarization: {e}")
        
        return text_to_summarize # Fallback to original text on any error