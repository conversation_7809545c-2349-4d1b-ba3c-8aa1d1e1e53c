import time
from sentence_transformers import SentenceTransformer
import numpy as np

class EmbeddingManager:
    """
    Manages the loading of the sentence transformer model and generation of text embeddings.
    """
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        """
        Initializes the EmbeddingManager and loads the specified sentence transformer model.

        Args:
            model_name (str): The name of the sentence transformer model to load.
                              Defaults to 'all-MiniLM-L6-v2'.
        """
        print(f"Initializing EmbeddingManager with model: {model_name}...")
        start_time = time.time()
        try:
            self.model = SentenceTransformer(model_name)
            end_time = time.time()
            print(f"SentenceTransformer model '{model_name}' loaded successfully in {end_time - start_time:.2f} seconds.")
        except Exception as e:
            print(f"Error loading SentenceTransformer model '{model_name}': {e}")
            print("Please ensure 'sentence-transformers' is installed and the model name is correct.")
            self.model = None

    def generate_embedding(self, text: str) -> np.ndarray | None:
        """
        Generates an embedding for the given text.
        """
        if self.model and text:
            return self.model.encode(text, convert_to_numpy=True)
        return None