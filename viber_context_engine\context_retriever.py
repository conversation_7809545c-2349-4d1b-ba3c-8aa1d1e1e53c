# file: viber_context_engine/context_retriever.py

import json

class ContextRetriever:
    """
    Builds the optimal context for an LLM call by synergizing three sources:
    1. Short-Term Memory (the most recent conversational turns).
    2. Long-Term Memory (a semantic search of the entire chat history).
    3. Code Context (a semantic search of the project's codebase).
    """
    SUMMARIZATION_THRESHOLD = 4000
    RECENT_HISTORY_TURNS = 6 # How many recent messages to consider for short-term memory
    MAX_SEMANTIC_HISTORY_RESULTS = 5 # How many relevant past conversations to find
     
    def __init__(self, embedding_manager, chat_history_vector_store, summarizer, examples_vector_store, project_indexer=None):
        self.embedding_manager = embedding_manager
        self.chat_history_vector_store = chat_history_vector_store
        self.summarizer = summarizer
        self.examples_vector_store = examples_vector_store
        self.project_indexer = project_indexer # <-- NEW: Store the project indexer

    def _get_short_term_memory(self, full_message_history: list) -> str:
        """Extracts the last few conversational turns as a string."""
        recent_messages = full_message_history[-(self.RECENT_HISTORY_TURNS + 1):-1]
        if not recent_messages:
            return ""
        return "\n".join([f"{msg['role']}: {msg.get('content', '')}" for msg in recent_messages])

    def _get_long_term_memory(self, query: str) -> str:
        """Performs a semantic search on the chat history vector store."""
        if not self.chat_history_vector_store or self.chat_history_vector_store.collection.count() == 0:
            return ""
            
        try:
            query_embedding = self.embedding_manager.generate_embedding(query)
            
            # --- THIS IS THE FIX ---
            # The original code had `if not query_embedding:`, which causes a ValueError with numpy arrays.
            # The correct check is `if query_embedding is None:`.
            if query_embedding is None:
                print("Warning: Could not generate embedding for long-term memory query.")
                return ""
            # --- END OF FIX ---
                
            results = self.chat_history_vector_store.collection.query(
                query_embeddings=[query_embedding],
                n_results=self.MAX_SEMANTIC_HISTORY_RESULTS
            )

            if not results or not results.get('documents') or not results['documents'][0]:
                return ""
            
            retrieved_history = "\n---\n".join(results['documents'][0])
            return f"--- Relevant Past Conversations ---\n{retrieved_history}"
        except Exception as e:
            print(f"Error querying chat history vector store: {e}")
            return ""
    
    def retrieve_and_build_context(self, current_query: str, full_message_history: list, recognized_intent=None) -> list:
        """
        Constructs the final list of messages for the API call using the
        new three-stage context retrieval process.
        """
        messages_for_api = list(full_message_history)
        
        short_term_memory = self._get_short_term_memory(full_message_history)
        long_term_memory = self._get_long_term_memory(current_query)
        
        project_context = ""
        if self.project_indexer:
            enriched_query = f"Current task: {current_query}\nRecent conversation topic: {short_term_memory}"
            project_context = self.project_indexer.query_index(enriched_query)

        raw_context_block = ""
        if short_term_memory:
            raw_context_block += f"--- Recent Conversation (Short-Term Memory) ---\n{short_term_memory}\n\n"
        if long_term_memory:
            raw_context_block += f"{long_term_memory}\n\n"
        if project_context and "No relevant code" not in project_context and "Project has not been indexed" not in project_context:
            raw_context_block += f"{project_context}\n\n"
        
        raw_context_block += f"--- Current User Query ---\n{current_query}"
        
        final_context_str = raw_context_block
        if len(raw_context_block) > self.SUMMARIZATION_THRESHOLD:
            if hasattr(self.summarizer, 'services'):
                self.summarizer.services.view.insert_message("System", f"Context is large ({len(raw_context_block)} chars). Condensing for clarity...", "bot_status_tag")
            
            condensed_summary = self.summarizer.summarize_text(raw_context_block)
            
            if condensed_summary:
                final_context_str = (
                    "--- Condensed Summary of Query and All Relevant Context ---\n"
                    "The following is a condensed summary of the user's request, past conversations, and relevant code. "
                    "Use this summary to formulate your response.\n\n"
                    f"{condensed_summary}"
                )
            else:
                if hasattr(self.summarizer, 'services'):
                    self.summarizer.services.view.insert_message("System", "Summarization failed. Using raw context.", "warning")
        
        if final_context_str.strip():
            context_message = {"role": "system", "content": final_context_str}
            messages_for_api.insert(-1, context_message)

        return messages_for_api