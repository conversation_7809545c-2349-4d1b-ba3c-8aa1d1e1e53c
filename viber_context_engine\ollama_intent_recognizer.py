import requests
import json
from typing import List, Dict, Optional

OLLAMA_API_URL = "http://localhost:11434/api/generate"
DEFAULT_INTENT_MODEL = "deepseek-coder-v2:latest" # As per your choice

# Define your specific coding intents
CODING_INTENTS = [
    'generate_code',
    'explain_code',
    'debug_error',
    'refactor_code',
    'file_system_query', # e.g., "list files", "read file" (though tools handle this)
    'general_coding_question',
    'code_completion',
    'documentation_lookup',
    'unknown_coding_intent', # If it's coding related but doesn't fit others
    'non_coding_query'
]

class OllamaIntentRecognizer:
    def __init__(self, 
                 model_name: str = DEFAULT_INTENT_MODEL, 
                 ollama_url: str = OLLAMA_API_URL,
                 default_options: Optional[Dict] = None):
        self.model_name = model_name
        self.ollama_url = ollama_url
        self.default_options = default_options if default_options is not None else {
            "temperature": 0.2, "num_predict": 20
        }
        print(f"OllamaIntentRecognizer initialized for model: {self.model_name} at {self.ollama_url}")

    def _build_intent_prompt(self, user_query: str) -> str:
        # Few-shot examples can be very effective here.
        # For now, a direct instruction prompt.
        # You can expand this with examples like shown in the Ollama chat.
        prompt = (
            f"You are an expert at classifying user intent for a coding assistant.\n"
            f"Given the user query: \"{user_query}\"\n"
            f"Classify the primary coding-related intent. Your response should be ONLY ONE of the following labels:\n"
            f"{', '.join(CODING_INTENTS)}\n"
            f"Intent:"
        )
        return prompt

    def recognize_intent(self, user_query: str, override_options: Optional[Dict] = None) -> Optional[str]:
        if not user_query:
            return None

        prompt_for_ollama = self._build_intent_prompt(user_query)
        
        payload = {
            "model": self.model_name,
            "prompt": prompt_for_ollama,
            "stream": False, # We want the full response for classification
            "options": override_options if override_options is not None else self.default_options
        }

        try:
            response = requests.post(self.ollama_url, json=payload, timeout=15) # 15s timeout
            response.raise_for_status()
            
            response_data = response.json()
            intent_label = response_data.get("response", "").strip().lower()
            
            if intent_label in [intent.lower() for intent in CODING_INTENTS]:
                print(f"Ollama recognized intent: {intent_label} for query: '{user_query[:50]}...'")
                return intent_label
            else:
                print(f"Ollama response '{intent_label}' not in defined intents. Defaulting or further processing needed.")
                return 'unknown_coding_intent' # Or None, or try to map it

        except requests.exceptions.RequestException as e:
            print(f"Error communicating with Ollama API at {self.ollama_url}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error decoding Ollama API response: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error during Ollama intent recognition: {e}")
            return None
